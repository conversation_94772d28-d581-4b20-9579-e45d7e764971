# ================================ 降维方法综合评价模块 ===================================
"""
降维方法综合评价模块 (Enhanced Dimensionality Reduction Evaluator)

本模块提供了一套完整的降维方法评价体系，包含以下七大评价维度：
1. 重构质量评价 - 衡量数据重构的准确性
2. 结构保持性评价 - 评估降维后数据结构的保持程度
3. 聚类性能评价 - 测试降维数据的聚类效果
4. 分类性能评价 - 评估降维数据的分类能力
5. 稳定性和鲁棒性评价 - 测试方法对噪声和参数变化的敏感性
6. 计算效率评价 - 评估时间和空间复杂度
7. 可解释性评价 - 衡量降维结果的可解释程度

作者：基于PCAWZH类改进和扩展
版本：2.0 Enhanced
更新日期：2025年
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import tracemalloc
import psutil
import warnings
from sklearn.metrics import mean_squared_error, pairwise_distances, accuracy_score
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from scipy import stats
from scipy.stats import friedmanchisquare, wilcoxon
from sklearn.feature_selection import mutual_info_regression
from sklearn.metrics import mutual_info_score
from sklearn.manifold import trustworthiness
from dtw import dtw
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics.cluster import adjusted_rand_score, adjusted_mutual_info_score
from sklearn.metrics import normalized_mutual_info_score, fowlkes_mallows_score
from sklearn import metrics
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import r2_score, mean_absolute_percentage_error
import nolds

# 忽略警告信息
warnings.filterwarnings('ignore')


# ================================ 辅助函数 ===================================

def sum_above_diagonal(matrix):
    """
    计算矩阵对角线及以上元素的和

    参数:
        matrix (np.ndarray): 输入的方阵

    返回:
        float: 对角线及以上元素的总和

    用途:
        用于量化转移矩阵的误差，主要关注上三角和对角线元素
    """
    n = len(matrix)
    diagonal_sum = sum(matrix[i][i] for i in range(n))  # 对角线元素和
    above_diagonal_sum = sum(matrix[i][j] for i in range(n) for j in range(i+1, n))  # 上三角元素和
    return diagonal_sum + above_diagonal_sum


def compute_transition_matrix(pred):
    """
    根据状态序列计算概率转移矩阵

    参数:
        pred (array-like): 状态序列，包含离散的状态标签

    返回:
        np.ndarray: 概率转移矩阵，元素(i,j)表示从状态i转移到状态j的概率

    算法原理:
        1. 统计相邻时间步之间的状态转移次数
        2. 按行归一化得到转移概率
        3. 处理零除法情况

    应用:
        用于马尔科夫建模评价，比较高维和低维数据的动态特性
    """
    num_states = max(pred) + 1
    transition_counts = np.zeros((num_states, num_states))

    # 统计状态转移次数
    for current_state, next_state in zip(pred[:-1], pred[1:]):
        transition_counts[current_state, next_state] += 1

    # 计算转移概率（按行归一化）
    row_sums = transition_counts.sum(axis=1, keepdims=True)
    transition_matrix = np.divide(transition_counts, row_sums, where=row_sums!=0)
    return transition_matrix


def uniform_init_centers(X, n_clusters, random_state=None):
    """
    均匀分布的KMeans初始化方法

    参数:
        X: 输入数据 (n_samples, n_features)
        n_clusters: 聚类中心数量
        random_state: 随机种子

    返回:
        centers: 初始聚类中心 (n_clusters, n_features)

    算法原理:
        1. 计算每个特征维度的最小值和最大值
        2. 在每个维度上均匀分布初始聚类中心
        3. 添加小的随机扰动避免完全重叠
        4. 确保初始点在数据空间内均匀分布

    优势:
        - 相比随机初始化，提供更稳定的聚类结果
        - 相比k-means++，计算更快且分布更均匀
        - 适合高维数据的聚类初始化
    """
    if random_state is not None:
        np.random.seed(random_state)

    n_samples, n_features = X.shape

    # 计算数据在每个维度上的范围
    min_vals = np.min(X, axis=0)
    max_vals = np.max(X, axis=0)

    centers = np.zeros((n_clusters, n_features))

    # 方法1: 基于数据范围的均匀分布
    for i in range(n_features):
        # 在每个维度上均匀分布初始点
        if max_vals[i] != min_vals[i]:  # 避免除零
            centers[:, i] = np.linspace(min_vals[i], max_vals[i], n_clusters)
        else:
            centers[:, i] = min_vals[i]  # 如果该维度数据相同，设为常数

    # 添加小的随机扰动以避免完全重叠，同时保持均匀性
    data_std = np.std(X, axis=0)
    noise_scale = 0.01 * data_std  # 扰动幅度为数据标准差的1%
    noise_scale = np.where(data_std > 0, noise_scale, 0.01)  # 处理标准差为0的情况

    centers += np.random.normal(0, noise_scale, centers.shape)

    # 确保初始点在数据范围内
    centers = np.clip(centers, min_vals, max_vals)

    return centers


def normalize_score(score, score_type='higher_better', min_val=None, max_val=None):
    """
    标准化评分到[0,1]范围

    参数:
        score (float or array): 原始评分
        score_type (str): 评分类型，'higher_better'或'lower_better'
        min_val (float): 最小值，用于标准化
        max_val (float): 最大值，用于标准化

    返回:
        float or array: 标准化后的评分
    """
    if isinstance(score, (list, np.ndarray)):
        score = np.array(score)
        if min_val is None:
            min_val = score.min()
        if max_val is None:
            max_val = score.max()
    else:
        if min_val is None:
            min_val = 0
        if max_val is None:
            max_val = 1

    # 避免除零
    if max_val == min_val:
        return 0.5

    # 标准化
    normalized = (score - min_val) / (max_val - min_val)

    # 如果是越小越好的指标，需要反转
    if score_type == 'lower_better':
        normalized = 1 - normalized

    return np.clip(normalized, 0, 1)


# ================================ 主评价类 ===================================

class EvaluationHyperparameters:
    """
    降维评价超参数配置类

    集中管理所有评价过程中使用的超参数，便于统一配置和调优
    """

    def __init__(self):
        # ================================ 核心配置参数 ===================================
        self.n_cluster = 15                    # 聚类数量，影响聚类性能评价
        self.random_state = 42                 # 随机种子，确保结果可重现

        # ================================ KMeans聚类参数 ===================================
        self.kmeans_max_iter = 500             # KMeans最大迭代次数
        self.kmeans_n_init = 1                 # KMeans运行次数（均匀初始化后设为1）
        self.kmeans_tolerance = 1e-4           # KMeans收敛容忍度

        # ================================ 全局结构保持性评价参数 ===================================
        self.density_k_neighbors = 10          # 密度评价的k近邻数量
        self.topology_n_neighbors = 5          # 拓扑结构评价的邻居数量
        self.numerical_epsilon = 1e-10         # 数值稳定性小常数

        # ================================ 稳定性评价参数 ===================================
        self.stability_n_runs = 10             # 稳定性测试运行次数
        self.stability_noise_level = 0.01      # 噪声水平（相对于数据标准差）

        # ================================ 分类性能评价参数 ===================================
        self.classification_test_size = 0.6    # 分类测试集比例
        self.classification_cv_folds = 5       # 交叉验证折数
        self.classification_random_state = 42  # 分类评价随机种子

        # ================================ 非线性特征评价参数 ===================================
        self.sampen_emb_dim = 2               # 样本熵嵌入维度
        self.sampen_tolerance = None          # 样本熵容忍度（None为自动计算）

        # ================================ 多尺度结构评价参数 ===================================
        self.multiscale_max_neighbors = 50    # 多尺度评价最大邻居数
        self.multiscale_scales = [5, 10, 20, 30, 50]  # 不同邻域尺度

        # ================================ 线性建模评价参数 ===================================
        self.linear_modeling_train_size = 0.8  # 线性建模训练集比例
        self.linear_modeling_random_state = 40 # 线性建模随机种子

        # ================================ 均匀初始化参数 ===================================
        self.uniform_init_noise_scale = 0.01   # 均匀初始化扰动比例

        # ================================ 距离计算参数 ===================================
        self.distance_metric = 'euclidean'     # 距离计算方法
        self.upper_triangle_offset = 1         # 上三角矩阵偏移量

    def update_for_data_size(self, n_samples):
        """
        根据数据规模自动调整超参数

        参数:
            n_samples (int): 样本数量
        """
        if n_samples < 100:
            # 小数据集
            self.n_cluster = min(5, n_samples // 10)
            self.density_k_neighbors = min(5, n_samples - 1)
            self.topology_n_neighbors = min(3, n_samples - 1)
            self.stability_n_runs = 5
        elif n_samples < 1000:
            # 中等数据集
            self.n_cluster = min(10, n_samples // 20)
            self.density_k_neighbors = min(10, n_samples - 1)
            self.topology_n_neighbors = min(5, n_samples - 1)
            self.stability_n_runs = 10
        else:
            # 大数据集
            self.n_cluster = min(15, n_samples // 50)
            self.density_k_neighbors = min(15, n_samples - 1)
            self.topology_n_neighbors = min(10, n_samples - 1)
            self.stability_n_runs = 15

        # 确保参数合理性
        self.multiscale_max_neighbors = min(self.multiscale_max_neighbors, n_samples - 1)
        self.multiscale_scales = [k for k in self.multiscale_scales if k < n_samples]
        if not self.multiscale_scales:
            self.multiscale_scales = [min(5, n_samples - 1)]

    def set_fast_mode(self):
        """设置快速评价模式（降低计算复杂度）"""
        self.kmeans_max_iter = 300
        self.stability_n_runs = 5
        self.multiscale_scales = [5, 10]
        self.classification_cv_folds = 3

    def set_precise_mode(self):
        """设置精确评价模式（提高评价精度）"""
        self.kmeans_max_iter = 1000
        self.stability_n_runs = 20
        self.multiscale_scales = [3, 5, 10, 15, 20, 30, 50]
        self.classification_cv_folds = 10

    def set_robust_mode(self):
        """设置鲁棒性测试模式（增强噪声测试）"""
        self.stability_noise_level = 0.05
        self.stability_n_runs = 15

    def get_config_summary(self):
        """获取配置摘要"""
        return {
            '核心参数': {
                '聚类数量': self.n_cluster,
                '随机种子': self.random_state
            },
            'KMeans参数': {
                '最大迭代': self.kmeans_max_iter,
                '运行次数': self.kmeans_n_init
            },
            '结构评价参数': {
                '密度k近邻': self.density_k_neighbors,
                '拓扑邻居数': self.topology_n_neighbors
            },
            '稳定性参数': {
                '运行次数': self.stability_n_runs,
                '噪声水平': self.stability_noise_level
            }
        }


class DimensionalityReductionEvaluator:
    """
    降维方法综合评价器 (Hyperparameter-Separated Version)

    该类提供了简洁而全面的降维方法评价功能，包含核心评价维度：
    1. 重构质量评价 - 衡量信息保持程度
    2. 全局结构保持性评价 - 衡量几何和拓扑结构保持
    3. 聚类性能评价 - 衡量聚类结构保持
    4. 分类性能评价 - 衡量监督学习性能
    5. 稳定性评价 - 衡量算法鲁棒性
    6. 计算效率评价 - 衡量算法实用性

    设计原则：
    - 超参数集中管理，算法运行前统一配置
    - 无权重机制，提供多指标公平对比
    - 算法失败直接报错，确保结果可靠性
    - 可解释性通过人工观察，不进行量化
    - 全局结构评价替代局部结构保持性

    使用示例:
        evaluator = DimensionalityReductionEvaluator()
        evaluator.config.n_cluster = 20  # 自定义超参数
        results = evaluator.comprehensive_evaluate(X_original, X_reduced, X_reconstructed)
    """

    def __init__(self, hyperparams=None):
        """
        初始化评价器

        参数:
            hyperparams (EvaluationHyperparameters): 超参数配置对象，None则使用默认配置

        属性:
            config (EvaluationHyperparameters): 超参数配置
            evaluation_history (list): 存储历史评价结果
        """
        # 超参数配置
        self.config = hyperparams if hyperparams is not None else EvaluationHyperparameters()
        self.evaluation_history = []

        # 设置随机种子
        np.random.seed(self.config.random_state)

    def _check_inverse_transform_support(self, method_name, reduction_method=None):
        """
        检测降维方法是否支持逆变换

        参数:
            method_name (str): 方法名称
            reduction_method: 方法对象

        返回:
            bool: 是否支持逆变换
        """
        # 基于方法名称的检测
        non_invertible_methods = {
            'TSNE', 't-SNE', 'tsne', 'T-SNE',
            'MDS', 'mds', 'MultidimensionalScaling',
            'Isomap', 'ISOMAP', 'isomap',
            'LLE', 'lle', 'LocallyLinearEmbedding',
            'UMAP', 'umap',
            'SpectralEmbedding', 'Laplacian',
            'DiffusionMaps'
        }

        if method_name in non_invertible_methods:
            return False

        # 基于方法对象的检测
        if reduction_method is not None:
            # 检查是否有inverse_transform方法
            if hasattr(reduction_method, 'inverse_transform'):
                return True

            # 检查方法类型
            method_type = type(reduction_method).__name__
            if method_type in non_invertible_methods:
                return False

        # 默认假设支持逆变换（如PCA、FastICA、NMF等）
        return True
        
    # ================================ 1. 重构质量评价 ===================================

    def evaluate_reconstruction_error(self, X_original, X_reconstructed, has_inverse_transform=True):
        """
        评价重构误差 - 衡量降维后重构数据与原始数据的差异

        参数:
            X_original (np.ndarray): 原始数据矩阵，形状为(n_samples, n_features)
            X_reconstructed (np.ndarray): 重构数据矩阵，形状与原始数据相同
            has_inverse_transform (bool): 是否支持逆变换，默认True

        返回:
            tuple: (frobenius_error, rmse_error, mae_error, relative_error)
            - frobenius_error: 相对Frobenius范数误差 [0, +∞)，越小越好
            - rmse_error: 均方根误差 [0, +∞)，越小越好
            - mae_error: 平均绝对误差 [0, +∞)，越小越好
            - relative_error: 相对误差 [0, +∞)，越小越好

        注意:
            - 对于不支持逆变换的方法（如t-SNE、MDS、Isomap、LLE），所有误差设为10000
            - 这是一个惩罚值，表示无法进行重构评价

        数学原理:
            - Frobenius范数: ||X_orig - X_recon||_F / ||X_orig||_F
            - RMSE: sqrt(MSE(X_orig, X_recon))
            - MAE: mean(|X_orig - X_recon|)
            - 相对误差: mean(|X_orig - X_recon| / (|X_orig| + ε))
        """
        # 检查是否支持逆变换
        if not has_inverse_transform:
            print("  警告: 该方法不支持逆变换，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        # 检查重构数据是否有效
        if X_reconstructed is None:
            print("  警告: 重构数据为None，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        # 检查数据形状是否匹配
        if X_original.shape != X_reconstructed.shape:
            print(f"  警告: 数据形状不匹配 {X_original.shape} vs {X_reconstructed.shape}，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        # 计算误差矩阵
        error_matrix = X_original - X_reconstructed

        # 1. Frobenius范数误差（相对）
        frobenius_error = np.linalg.norm(error_matrix) / (np.linalg.norm(X_original) + 1e-10)

        # 2. RMSE误差
        mse = mean_squared_error(X_original, X_reconstructed)
        rmse_error = np.sqrt(mse)

        # 3. 平均绝对误差
        mae_error = np.mean(np.abs(error_matrix))

        # 4. 相对误差（避免除零）
        relative_error = np.mean(np.abs(error_matrix) / (np.abs(X_original) + 1e-10))

        return frobenius_error, rmse_error, mae_error, relative_error
    
    def evaluate_global_structure_preservation(self, X_original, X_reduced):
        """
        评价全局结构保持性

        参数:
            X_original: 原始高维数据
            X_reduced: 降维后数据

        返回:
            dict: 包含多个全局结构指标的字典
        """
        # 1. 全局距离相关性
        high_dist = pairwise_distances(X_original, metric='euclidean')
        low_dist = pairwise_distances(X_reduced, metric='euclidean')

        high_dist_flat = high_dist[np.triu_indices_from(high_dist, k=1)]
        low_dist_flat = low_dist[np.triu_indices_from(low_dist, k=1)]
        distance_correlation = np.corrcoef(high_dist_flat, low_dist_flat)[0, 1]

        # 2. 距离排序相关性（Spearman相关）
        from scipy.stats import spearmanr
        rank_correlation, _ = spearmanr(high_dist_flat, low_dist_flat)

        # 3. 密度分布保持性
        from sklearn.neighbors import NearestNeighbors
        k = min(self.config.density_k_neighbors, X_original.shape[0] - 1)

        # 原始空间密度
        nbrs_high = NearestNeighbors(n_neighbors=k).fit(X_original)
        distances_high, _ = nbrs_high.kneighbors(X_original)
        density_high = 1.0 / (distances_high[:, -1] + self.config.numerical_epsilon)

        # 降维空间密度
        nbrs_low = NearestNeighbors(n_neighbors=k).fit(X_reduced)
        distances_low, _ = nbrs_low.kneighbors(X_reduced)
        density_low = 1.0 / (distances_low[:, -1] + self.config.numerical_epsilon)

        density_preservation = np.corrcoef(density_high, density_low)[0, 1]

        # 4. 拓扑结构保持性
        n_neighbors = min(self.config.topology_n_neighbors, X_original.shape[0] - 1)
        trustworthiness_score = trustworthiness(X_original, X_reduced, n_neighbors=n_neighbors)

        # 5. 全局方差保持比例
        var_original = np.var(X_original, axis=0).sum()
        var_reduced = np.var(X_reduced, axis=0).sum()
        global_variance_ratio = var_reduced / var_original if var_original > 0 else 0

        return {
            'distance_correlation': distance_correlation,
            'rank_correlation': rank_correlation,
            'density_preservation': density_preservation,
            'trustworthiness': trustworthiness_score,
            'global_variance_ratio': global_variance_ratio
        }
    
    def evaluate_clustering_performance(self, X_original, X_reduced):
        """
        评价聚类性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        rand_score: 调整兰德指数
        nmi_score: 标准化互信息
        fmi_score: Fowlkes-Mallows指数
        silhouette_score: 轮廓系数
        """
        # 高维聚类 - 使用均匀分布初始化的 KMeans
        high_init_centers = uniform_init_centers(X_original, self.config.n_cluster, self.config.random_state)
        high_kmeans = KMeans(n_clusters=self.config.n_cluster, max_iter=self.config.kmeans_max_iter,
                            init=high_init_centers, random_state=self.config.random_state,
                            n_init=self.config.kmeans_n_init, tol=self.config.kmeans_tolerance)
        high_labels = high_kmeans.fit_predict(X_original)

        # 低维聚类 - 使用均匀分布初始化的 KMeans
        low_init_centers = uniform_init_centers(X_reduced, self.config.n_cluster, self.config.random_state)
        low_kmeans = KMeans(n_clusters=self.config.n_cluster, max_iter=self.config.kmeans_max_iter,
                           init=low_init_centers, random_state=self.config.random_state,
                           n_init=self.config.kmeans_n_init, tol=self.config.kmeans_tolerance)
        low_labels = low_kmeans.fit_predict(X_reduced)

        # 聚类一致性评价
        rand_score = adjusted_rand_score(low_labels, high_labels)
        nmi_score = normalized_mutual_info_score(low_labels, high_labels)
        fmi_score = fowlkes_mallows_score(low_labels, high_labels)

        # 轮廓系数
        silhouette_score = metrics.silhouette_score(X_reduced, low_labels, metric='euclidean')

        return rand_score, nmi_score, fmi_score, silhouette_score
    
    def evaluate_classification_performance(self, X_original, X_reduced):
        """
        评价分类性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        classification_score: 分类准确率
        """
        # 使用原始数据的聚类结果作为标签 - 使用均匀分布初始化的 KMeans
        init_centers = uniform_init_centers(X_original, self.config.n_cluster, self.config.random_state)
        kmeans = KMeans(n_clusters=self.config.n_cluster, max_iter=self.config.kmeans_max_iter,
                       init=init_centers, random_state=self.config.random_state,
                       n_init=self.config.kmeans_n_init, tol=self.config.kmeans_tolerance)
        labels = kmeans.fit_predict(X_original)

        # 构建标记数据集
        labeled_dataset = np.column_stack([X_reduced, labels])
        np.random.shuffle(labeled_dataset)

        data = labeled_dataset[:, :-1]
        target = labeled_dataset[:, -1]

        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(
            data, target, test_size=self.config.classification_test_size,
            random_state=self.config.classification_random_state
        )

        # 网格搜索最优参数
        parameters = {'n_estimators': [5, 10, 15, 20, 25, 30], 'max_depth': [2, 4, 6, 8, 10, 12]}
        clf = GridSearchCV(
            RandomForestClassifier(random_state=self.config.classification_random_state),
            parameters, cv=self.config.classification_cv_folds, scoring='accuracy', n_jobs=-1
        )
        clf.fit(X_train, y_train)

        return clf.best_score_
    
    def evaluate_markov_modeling(self, X_original, X_reduced):
        """
        评价马尔科夫建模性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        transition_error: 转移矩阵误差
        """
        # 高维聚类 - 使用均匀分布初始化的 KMeans
        high_init_centers = uniform_init_centers(X_original, self.config.n_cluster, self.config.random_state)
        high_kmeans = KMeans(n_clusters=self.config.n_cluster, max_iter=self.config.kmeans_max_iter,
                            init=high_init_centers, random_state=self.config.random_state,
                            n_init=self.config.kmeans_n_init, tol=self.config.kmeans_tolerance)
        high_labels = high_kmeans.fit_predict(X_original)

        # 低维聚类 - 使用均匀分布初始化的 KMeans
        low_init_centers = uniform_init_centers(X_reduced, self.config.n_cluster, self.config.random_state)
        low_kmeans = KMeans(n_clusters=self.config.n_cluster, max_iter=self.config.kmeans_max_iter,
                           init=low_init_centers, random_state=self.config.random_state,
                           n_init=self.config.kmeans_n_init, tol=self.config.kmeans_tolerance)
        low_labels = low_kmeans.fit_predict(X_reduced)
        
        # 计算转移矩阵
        high_tpm = compute_transition_matrix(high_labels)
        low_tpm = compute_transition_matrix(low_labels)
        
        # 计算误差
        err_tpm = low_tpm - high_tpm
        transition_error = sum_above_diagonal(np.abs(err_tpm))
        
        return transition_error, high_tpm, low_tpm
    
    def evaluate_nonlinear_features(self, X_reduced):
        """
        评价非线性特征
        参数:
        X_reduced: 降维数据
        返回:
        sampen_mean: 样本熵均值
        hurst_mean: Hurst指数均值
        lyap_mean: 李雅普诺夫指数均值
        """
        n_components = X_reduced.shape[1]
        
        # 样本熵
        sampens = []
        for i in range(n_components):
            sampen = nolds.sampen(X_reduced[:, i], emb_dim=self.config.sampen_emb_dim,
                                tolerance=self.config.sampen_tolerance)
            sampens.append(sampen)
        sampen_mean = np.mean(sampens)
        
        # Hurst指数
        hursts = []
        for i in range(n_components):
            hurst = nolds.hurst_rs(X_reduced[:, i])
            hursts.append(hurst)
        hurst_mean = np.mean(hursts)
        
        # 李雅普诺夫指数
        lyaps = []
        for i in range(n_components):
            lyap = nolds.lyap_r(X_reduced[:, i])
            lyaps.append(lyap)
        lyap_mean = np.mean(lyaps)
        
        return sampen_mean, hurst_mean, lyap_mean
    
    def evaluate_linear_modeling(self, X_reduced, dims=[2, 3], degrees=[1, 2, 3]):
        """
        评价线性建模性能
        参数:
        X_reduced: 降维数据
        dims: 建模维度列表
        degrees: 多项式阶数列表
        返回:
        r2_scores: R²分数数组
        mse_scores: MSE分数数组
        """
        r2_scores = []
        mse_scores = []
        
        for dim in dims:
            for degree in degrees:
                if dim > X_reduced.shape[1]:
                    continue
                    
                # 选择前dim个维度
                data_subset = X_reduced[:, :dim]
                
                # 计算梯度作为因变量
                t = 0.01
                gradients = []
                for i in range(dim):
                    gradient = np.gradient(data_subset[:, i], t)
                    gradients.append(gradient)
                target = np.array(gradients).T
                
                # 数据划分
                X_train, X_test, y_train, y_test = train_test_split(
                    data_subset, target, train_size=self.config.linear_modeling_train_size,
                    random_state=self.config.linear_modeling_random_state
                )
                
                # 标准化
                scaler_X = StandardScaler()
                X_train_scaled = scaler_X.fit_transform(X_train)
                X_test_scaled = scaler_X.transform(X_test)
                
                scaler_y = StandardScaler()
                y_train_scaled = scaler_y.fit_transform(y_train)
                y_test_scaled = scaler_y.transform(y_test)
                
                # 多项式特征
                poly = PolynomialFeatures(degree=degree)
                X_poly = poly.fit_transform(data_subset)[:, 1:]  # 去除常数项
                
                # 线性回归
                model = LinearRegression(fit_intercept=False)
                model.fit(X_poly, target)
                
                # 预测和评价
                y_pred = model.predict(X_poly)
                r2 = r2_score(target, y_pred)
                mse = mean_squared_error(target, y_pred)
                
                r2_scores.append(r2)
                mse_scores.append(mse)
        
        return np.array(r2_scores), np.array(mse_scores)

    # ================================ 5. 稳定性和鲁棒性评价 ===================================

    def evaluate_stability(self, X_original, reduction_method, n_components):
        """
        评价降维方法的稳定性和鲁棒性

        参数:
            X_original (np.ndarray): 原始数据
            reduction_method: 降维方法对象（需要有fit_transform方法）
            n_components (int): 降维维数

        返回:
            dict: 包含稳定性评价结果
            - stability_score: 稳定性分数 [0, 1]，越大越稳定
            - noise_robustness: 噪声鲁棒性 [0, 1]，越大越鲁棒

        评价原理:
            1. 多次运行同一方法，计算结果的方差
            2. 添加不同水平的噪声，测试鲁棒性
        """
        results_clean = []
        results_noisy = []

        # 计算噪声标准差
        noise_std = self.config.stability_noise_level * np.std(X_original)

        for run in range(self.config.stability_n_runs):
            # 设置不同的随机种子
            if hasattr(reduction_method, 'random_state'):
                reduction_method.random_state = self.config.random_state + run

            # 1. 干净数据的结果
            X_reduced_clean = reduction_method.fit_transform(X_original)
            results_clean.append(X_reduced_clean)

            # 2. 加噪声数据的结果
            X_noisy = X_original + np.random.normal(0, noise_std, X_original.shape)
            X_reduced_noisy = reduction_method.fit_transform(X_noisy)
            results_noisy.append(X_reduced_noisy)

        # 计算稳定性指标
        results_clean = np.array(results_clean)
        results_noisy = np.array(results_noisy)

        # 1. 稳定性分数（基于方差）
        if len(results_clean) > 1:
            variance_per_point = np.var(results_clean, axis=0)
            mean_variance = np.mean(variance_per_point)
            stability_score = 1 / (1 + mean_variance)  # 转换为[0,1]，越大越稳定
        else:
            stability_score = 1.0

        # 2. 噪声鲁棒性
        if len(results_noisy) > 0 and len(results_clean) > 0:
            # 计算加噪声前后结果的相似性
            similarity_scores = []
            for i in range(min(len(results_clean), len(results_noisy))):
                # 使用余弦相似度
                clean_flat = results_clean[i].flatten()
                noisy_flat = results_noisy[i].flatten()

                # 避免零向量
                if np.linalg.norm(clean_flat) > 1e-10 and np.linalg.norm(noisy_flat) > 1e-10:
                    similarity = np.dot(clean_flat, noisy_flat) / (
                        np.linalg.norm(clean_flat) * np.linalg.norm(noisy_flat)
                    )
                    similarity_scores.append(max(0, similarity))  # 确保非负

            noise_robustness = np.mean(similarity_scores) if similarity_scores else 0.5
        else:
            noise_robustness = 0.5

        return {
            'stability_score': stability_score,
            'noise_robustness': noise_robustness,
            'variance_details': np.mean(variance_per_point) if len(results_clean) > 1 else 0
        }

    # ================================ 6. 计算效率评价 ===================================

    def evaluate_computational_efficiency(self, X_original, reduction_method, n_components):
        """
        评价计算效率

        参数:
            X_original (np.ndarray): 原始数据
            reduction_method: 降维方法对象
            n_components (int): 降维维数

        返回:
            dict: 效率评价结果
            - time_cost: 时间消耗（秒）
            - memory_usage: 内存使用（MB）
            - scalability: 可扩展性指标（时间/样本数）
            - efficiency_score: 综合效率分数 [0, 1]
        """
        # 获取当前进程
        process = psutil.Process()

        # 记录初始内存
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        # 记录开始时间
        start_time = time.time()

        # 执行降维
        X_reduced = reduction_method.fit_transform(X_original)

        # 记录结束时间
        end_time = time.time()

        # 记录结束内存
        memory_after = process.memory_info().rss / 1024 / 1024  # MB

        # 计算指标
        time_cost = end_time - start_time
        memory_usage = max(0, memory_after - memory_before)
        scalability = time_cost / X_original.shape[0] if X_original.shape[0] > 0 else 0

        # 计算效率分数（基于时间和内存的综合评价）
        # 使用对数变换，避免极值影响
        time_score = 1 / (1 + np.log10(max(time_cost, 0.001)))
        memory_score = 1 / (1 + np.log10(max(memory_usage, 0.1)))
        efficiency_score = (time_score + memory_score) / 2

        return {
            'time_cost': time_cost,
            'memory_usage': memory_usage,
            'scalability': scalability,
            'efficiency_score': efficiency_score,
            'time_score': time_score,
            'memory_score': memory_score
        }

    # ================================ 注意：可解释性评价 ===================================
    # 可解释性不进行量化评价，通过人工观察降维后的结果来判断
    # 建议观察以下方面：
    # 1. 降维后数据的可视化效果
    # 2. 主成分的物理意义（对于线性方法）
    # 3. 聚类结构的清晰度
    # 4. 异常点的识别能力

    # ================================ 8. 多尺度结构评价 ===================================

    def evaluate_multiscale_structure(self, X_original, X_reduced):
        """
        评价多尺度结构保持性

        参数:
            X_original (np.ndarray): 原始高维数据
            X_reduced (np.ndarray): 降维后数据

        返回:
            dict: 多尺度结构评价结果
            - multiscale_scores: 不同尺度下的trustworthiness分数
            - average_preservation: 平均结构保持性
            - scale_consistency: 尺度一致性
        """
        # 定义不同的邻域尺度
        max_neighbors = min(self.config.multiscale_max_neighbors, X_original.shape[0] - 1)
        scales = [k for k in self.config.multiscale_scales if k < max_neighbors]

        if not scales:
            scales = [min(5, max_neighbors)]

        preservation_scores = []

        for k in scales:
            score = trustworthiness(X_original, X_reduced, n_neighbors=k, metric='euclidean')
            preservation_scores.append(score)

        # 计算统计指标
        average_preservation = np.mean(preservation_scores)
        scale_consistency = 1 - np.std(preservation_scores)  # 一致性：标准差越小越一致

        return {
            'scales': scales,
            'multiscale_scores': preservation_scores,
            'average_preservation': average_preservation,
            'scale_consistency': max(0, scale_consistency)  # 确保非负
        }

    # ================================ 9. 综合评价方法 ===================================

    def comprehensive_evaluate(self, X_original, X_reduced, X_reconstructed=None,
                              reduction_method=None, method_name="Unknown",
                              has_inverse_transform=None):
        """
        综合评价降维方法的所有指标

        参数:
            X_original (np.ndarray): 原始数据
            X_reduced (np.ndarray): 降维后数据
            X_reconstructed (np.ndarray, optional): 重构数据
            reduction_method (optional): 降维方法对象
            method_name (str): 方法名称
            has_inverse_transform (bool, optional): 是否支持逆变换，自动检测

        返回:
            dict: 综合评价结果
        """
        print(f"开始综合评价降维方法: {method_name}")

        # 根据数据规模自动调整超参数
        self.config.update_for_data_size(X_original.shape[0])
        print(f"  - 根据数据规模({X_original.shape[0]}样本)自动调整超参数")
        print(f"  - 聚类数量: {self.config.n_cluster}, 密度k近邻: {self.config.density_k_neighbors}")

        # 自动检测是否支持逆变换
        if has_inverse_transform is None:
            has_inverse_transform = self._check_inverse_transform_support(method_name, reduction_method)

        results = {
            'method_name': method_name,
            'has_inverse_transform': has_inverse_transform,
            'data_info': {
                'n_samples': X_original.shape[0],
                'n_features_original': X_original.shape[1],
                'n_features_reduced': X_reduced.shape[1],
                'compression_ratio': X_reduced.shape[1] / X_original.shape[1]
            }
        }

        # 1. 重构质量评价
        print("  - 评价重构质量...")
        frobenius_error, rmse_error, mae_error, relative_error = self.evaluate_reconstruction_error(
            X_original, X_reconstructed, has_inverse_transform
        )
        results['reconstruction_quality'] = {
            'frobenius_error': frobenius_error,
            'rmse_error': rmse_error,
            'mae_error': mae_error,
            'relative_error': relative_error,
            'quality_score': normalize_score(frobenius_error, 'lower_better') if frobenius_error < 1000 else 0.0,
            'has_reconstruction': has_inverse_transform
        }
        # 2. 全局结构保持性评价
        print("  - 评价全局结构保持性...")
        structure_results = self.evaluate_global_structure_preservation(X_original, X_reduced)
        results['structure_preservation'] = structure_results

        # 3. 多尺度结构评价
        print("  - 评价多尺度结构...")
        multiscale_results = self.evaluate_multiscale_structure(X_original, X_reduced)
        results['multiscale_structure'] = multiscale_results

        # 4. 聚类性能评价
        print("  - 评价聚类性能...")
        rand_score, nmi_score, fmi_score, silhouette_score = self.evaluate_clustering_performance(
            X_original, X_reduced
        )
        results['clustering_performance'] = {
            'rand_score': rand_score,
            'nmi_score': nmi_score,
            'fmi_score': fmi_score,
            'silhouette_score': silhouette_score,
            'clustering_score': (rand_score + nmi_score + fmi_score) / 3
        }

        # 5. 分类性能评价
        print("  - 评价分类性能...")
        classification_score = self.evaluate_classification_performance(X_original, X_reduced)
        results['classification_performance'] = {
            'accuracy': classification_score,
            'classification_score': classification_score
        }

        # 6. 马尔科夫建模评价
        print("  - 评价马尔科夫建模...")
        transition_error, _, _ = self.evaluate_markov_modeling(X_original, X_reduced)
        results['markov_modeling'] = {
            'transition_error': transition_error,
            'markov_score': normalize_score(transition_error, 'lower_better')
        }

        # 7. 非线性特征评价
        print("  - 评价非线性特征...")
        sampen_mean, hurst_mean, lyap_mean = self.evaluate_nonlinear_features(X_reduced)
        results['nonlinear_features'] = {
            'sampen_mean': sampen_mean,
            'hurst_mean': hurst_mean,
            'lyap_mean': lyap_mean,
            'nonlinear_score': (sampen_mean + abs(hurst_mean - 0.5) * 2) / 2
        }

        # 8. 稳定性评价（如果提供了方法对象）
        if reduction_method is not None:
            print("  - 评价稳定性...")
            stability_results = self.evaluate_stability(
                X_original, reduction_method, X_reduced.shape[1]
            )
            results['stability'] = stability_results

            # 9. 效率评价
            print("  - 评价计算效率...")
            efficiency_results = self.evaluate_computational_efficiency(
                X_original, reduction_method, X_reduced.shape[1]
            )
            results['efficiency'] = efficiency_results

            # 10. 可解释性评价已删除
            print("  - 可解释性评价已跳过（方法已删除）")

        # 11. 多指标汇总（无权重机制）
        print("  - 汇总多指标评价结果...")
        results['evaluation_summary'] = self.summarize_multi_metrics(results)

        # 保存到历史记录
        self.evaluation_history.append(results)

        print(f"评价完成! 共评价 {len(results['evaluation_summary']['metrics_summary'])} 个指标")
        return results

    def summarize_multi_metrics(self, results):
        """
        多指标汇总（无权重机制）

        参数:
            results (dict): 评价结果字典

        返回:
            dict: 多指标汇总结果
        """
        metrics_summary = {}

        # 1. 重构质量指标
        if 'reconstruction_quality' in results:
            recon = results['reconstruction_quality']
            metrics_summary['重构质量'] = {
                'Frobenius误差': recon.get('frobenius_error', 'N/A'),
                'RMSE误差': recon.get('rmse_error', 'N/A'),
                'MAE误差': recon.get('mae_error', 'N/A'),
                '相对误差': recon.get('relative_error', 'N/A'),
                '支持重构': recon.get('has_reconstruction', False)
            }

        # 2. 全局结构保持性指标
        if 'structure_preservation' in results:
            struct = results['structure_preservation']
            metrics_summary['全局结构保持'] = {
                '距离相关性': struct.get('distance_correlation', 'N/A'),
                '排序相关性': struct.get('rank_correlation', 'N/A'),
                '密度保持性': struct.get('density_preservation', 'N/A'),
                '可信度': struct.get('trustworthiness', 'N/A'),
                '方差保持比': struct.get('global_variance_ratio', 'N/A')
            }

        # 3. 聚类性能指标
        if 'clustering_performance' in results:
            cluster = results['clustering_performance']
            metrics_summary['聚类性能'] = {
                '调整兰德指数': cluster.get('rand_score', 'N/A'),
                '标准化互信息': cluster.get('nmi_score', 'N/A'),
                'Fowlkes-Mallows': cluster.get('fmi_score', 'N/A'),
                '轮廓系数': cluster.get('silhouette_score', 'N/A')
            }

        # 4. 分类性能指标
        if 'classification_performance' in results:
            classif = results['classification_performance']
            metrics_summary['分类性能'] = {
                '准确率': classif.get('accuracy', 'N/A')
            }

        # 5. 稳定性指标
        if 'stability' in results:
            stab = results['stability']
            metrics_summary['稳定性'] = {
                '稳定性分数': stab.get('stability_score', 'N/A'),
                '噪声鲁棒性': stab.get('noise_robustness', 'N/A'),
                '方差详情': stab.get('variance_details', 'N/A')
            }

        # 6. 计算效率指标
        if 'efficiency' in results:
            eff = results['efficiency']
            metrics_summary['计算效率'] = {
                '时间开销(秒)': eff.get('time_cost', 'N/A'),
                '内存使用(MB)': eff.get('memory_usage', 'N/A'),
                '可扩展性': eff.get('scalability', 'N/A')
            }

        return {
            'metrics_summary': metrics_summary,
            'evaluation_principle': '多指标公平对比，无权重机制',
            'total_metrics': len(metrics_summary),
            'method_name': results.get('method_name', 'Unknown'),
            'data_info': results.get('data_info', {})
        }

    def compare_methods(self, results_list):
        """
        多方法评价结果比较（无权重机制）

        参数:
            results_list (list): 多个方法的评价结果列表

        返回:
            dict: 方法比较结果
        """
        if len(results_list) < 2:
            return {"error": "需要至少2个方法进行比较"}

        comparison_results = {
            'total_methods': len(results_list),
            'methods': [result['method_name'] for result in results_list],
            'metrics_comparison': {}
        }

        # 定义要比较的指标
        metrics_to_compare = {
            'reconstruction_quality': '重构质量',
            'structure_preservation': '全局结构保持',
            'clustering_performance': '聚类性能',
            'classification_performance': '分类性能',
            'stability': '稳定性',
            'efficiency': '计算效率'
        }

        # 对每个指标进行比较
        for metric_key, metric_name in metrics_to_compare.items():
            metric_comparison = {
                'metric_name': metric_name,
                'method_scores': {},
                'ranking': []
            }

            # 收集各方法在该指标上的表现
            for result in results_list:
                method_name = result['method_name']
                if metric_key in result:
                    if 'evaluation_summary' in result:
                        # 从汇总中提取指标信息
                        summary = result['evaluation_summary']['metrics_summary']
                        if metric_name in summary:
                            metric_comparison['method_scores'][method_name] = summary[metric_name]
                        else:
                            metric_comparison['method_scores'][method_name] = "未评价"
                    else:
                        metric_comparison['method_scores'][method_name] = "数据不完整"
                else:
                    metric_comparison['method_scores'][method_name] = "未评价"

            comparison_results['metrics_comparison'][metric_key] = metric_comparison

        return comparison_results



    def get_evaluation_summary(self):
        """
        获取评价历史的摘要（多指标展示）

        返回:
            dict: 评价摘要
        """
        if not self.evaluation_history:
            return {"message": "暂无评价历史"}

        summary = {
            'total_evaluations': len(self.evaluation_history),
            'methods_evaluated': [result['method_name'] for result in self.evaluation_history],
            'evaluation_principle': '多指标公平对比，无权重机制',
            'metrics_overview': {}
        }

        # 统计各方法的指标表现
        for result in self.evaluation_history:
            method_name = result['method_name']
            if 'evaluation_summary' in result:
                metrics_summary = result['evaluation_summary']['metrics_summary']
                summary['metrics_overview'][method_name] = {
                    'total_metrics': len(metrics_summary),
                    'metrics_list': list(metrics_summary.keys())
                }

        return summary
