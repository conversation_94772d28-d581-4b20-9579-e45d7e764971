# ================================ 导入库模块 ===================================
import os
import h5py
import numpy as np
import pandas as pd
import xarray as xr
import seaborn as sns
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import font_manager
import scipy.stats as ss
from scipy.stats import pearsonr
from scipy import stats
from PIL import Image
from tqdm import tqdm
import time
from scipy import fftpack
import scipy.integrate
from sklearn.metrics import mean_squared_error
from scipy.io import loadmat
from warnings import filterwarnings
from scipy.interpolate import interp2d
from scipy import signal
# 降维算法导入
from sklearn import decomposition
from sklearn.decomposition import PCA, FastICA, FactorAnalysis, IncrementalPCA, KernelPCA, SparsePCA
from sklearn.manifold import MDS, TSNE, LocallyLinearEmbedding, Isomap, SpectralEmbedding
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis as LDA  # 可以分类和降维
from sklearn.discriminant_analysis import QuadraticDiscriminantAnalysis as QDA  # 只能分类
from sklearn.manifold._locally_linear import barycenter_kneighbors_graph
from sklearn.metrics import euclidean_distances
from sklearn.metrics.pairwise import rbf_kernel
from sklearn.neighbors import kneighbors_graph

# 颜色映射设置
import cmasher as cmr
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1, N=64)      # 速度场颜色映射
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1, N=64)       # 压力场颜色映射
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1)              # 涡量场颜色映射
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1)          # 水相颜色映射
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1, N=64)       # 通用颜色映射
# 备选颜色映射：'RdBu_r'(红蓝白)，'RdBu'（红蓝反向），'rainbow'(红蓝)

# 忽略警告信息
filterwarnings('ignore')

# ================================ 字体设置函数 =================================
# 设置字体优先级列表
plt.rcParams['font.family'] = ['Times New Roman', 'SimHei']
plt.rcParams['font.sans-serif'] = ['Times New Roman', 'SimHei', 'DejaVu Sans']
plt.rcParams['font.serif'] = ['Times New Roman', 'SimSun', 'DejaVu Serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False

# 设置图形质量
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

# 设置标签和标题样式
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 10

# 设置线条和网格
plt.rcParams['lines.linewidth'] = 1.5
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.grid'] = True

# ================================================================================
# 数据集1：Re=100时2D圆柱绕流数据集 (Cylinder2D_Dataset_C)
# ================================================================================
"""
数据集描述：
- 数据来源：HFM-master/PINNData/Cylinder2D.mat
- 物理特征：雷诺数Re=100的层流圆柱绕流
- 空间维度：2D平面流动
- 时间采样：时间间隔0.08s
- 数据类型：包含浓度场、速度场、压力场的时空演化数据
- 应用场景：用于验证降维算法在层流条件下的性能
"""

# 数据文件路径
cylinder2d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D.mat"
# 备选数据路径（花瓣形局部观测）
# cylinder2d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_flower.mat"

# 加载Cylinder2D数据集
print("正在加载Cylinder2D数据集...")
cylinder2d_raw_data = loadmat(cylinder2d_data_path)

# 数据稀疏化参数设置
cylinder2d_sparse_factor = 1  # 稀疏化因子，1表示不稀疏化

# 提取Cylinder2D数据集的各个物理场
cylinder2d_concentration = cylinder2d_raw_data["C_star"][::cylinder2d_sparse_factor, :]  # 浓度场 C (N×T)
cylinder2d_velocity_x = cylinder2d_raw_data["U_star"][::cylinder2d_sparse_factor, :]     # X方向速度 (N×T)
cylinder2d_velocity_y = cylinder2d_raw_data["V_star"][::cylinder2d_sparse_factor, :]     # Y方向速度 (N×T)
cylinder2d_pressure = cylinder2d_raw_data["P_star"][::cylinder2d_sparse_factor, :]       # 压力场 (N×T)
cylinder2d_time = cylinder2d_raw_data["t_star"][::cylinder2d_sparse_factor, :]           # 时间序列 (T×1)
cylinder2d_coord_x = cylinder2d_raw_data["x_star"][::cylinder2d_sparse_factor, :]        # X坐标 (N×1)
cylinder2d_coord_y = cylinder2d_raw_data["y_star"][::cylinder2d_sparse_factor, :]        # Y坐标 (N×1)

# 数据集基本信息输出
print(f"Cylinder2D数据集信息：")
print(f"  - 空间点数：{cylinder2d_concentration.shape[0]}")
print(f"  - 时间步数：{cylinder2d_concentration.shape[1]}")
print(f"  - 时间范围：{cylinder2d_time.min():.3f} - {cylinder2d_time.max():.3f} s")
print(f"  - 空间范围：X[{cylinder2d_coord_x.min():.2f}, {cylinder2d_coord_x.max():.2f}], Y[{cylinder2d_coord_y.min():.2f}, {cylinder2d_coord_y.max():.2f}]")

# Cylinder2D数据集可视化 - 浓度场快照
cylinder2d_snapshot_time = 0  # 选择的快照时间步
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(cylinder2d_coord_x[:, cylinder2d_snapshot_time],
              cylinder2d_coord_y[:, cylinder2d_snapshot_time],
              cylinder2d_concentration[:, cylinder2d_snapshot_time],
              shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar(label='浓度 ')
plt.title(f'Cylinder2D数据集 - 浓度场快照 (t={cylinder2d_snapshot_time})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# ================================================================================
# 数据集2：Re=100时3D圆柱绕流数据集 (Cylinder3D_Dataset_E)
# ================================================================================
"""
数据集描述：
- 数据来源：HFM-master/PINNData/Cylinder3D.mat
- 物理特征：雷诺数Re=100的层流圆柱绕流
- 空间维度：3D立体流动
- 数据类型：包含浓度场、三维速度场、压力场的时空演化数据
- 应用场景：用于验证降维算法在三维流动中的性能
"""

# 数据文件路径
cylinder3d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder3D.mat"

# 加载Cylinder3D数据集
print("正在加载Cylinder3D数据集...")
cylinder3d_raw_data = loadmat(cylinder3d_data_path)

# 提取Cylinder3D数据集的各个物理场
cylinder3d_concentration = cylinder3d_raw_data["C_star"]  # 浓度场 C (N×T)
cylinder3d_velocity_x = cylinder3d_raw_data["U_star"]     # X方向速度 (N×T)
cylinder3d_velocity_y = cylinder3d_raw_data["V_star"]     # Y方向速度 (N×T)
cylinder3d_velocity_z = cylinder3d_raw_data["W_star"]     # Z方向速度 (N×T)
cylinder3d_pressure = cylinder3d_raw_data["P_star"]       # 压力场 (N×T)
cylinder3d_time = cylinder3d_raw_data["t_star"]           # 时间序列 (T×1)
cylinder3d_coord_x = cylinder3d_raw_data["x_star"]        # X坐标 (N×1)
cylinder3d_coord_y = cylinder3d_raw_data["y_star"]        # Y坐标 (N×1)
cylinder3d_coord_z = cylinder3d_raw_data["z_star"]        # Z坐标 (N×1)

# 数据集基本信息输出
print(f"Cylinder3D数据集信息：")
print(f"  - 空间点数：{cylinder3d_concentration.shape[0]}")
print(f"  - 时间步数：{cylinder3d_concentration.shape[1]}")
print(f"  - 时间范围：{cylinder3d_time.min():.3f} - {cylinder3d_time.max():.3f} s")
print(f"  - 空间范围：X[{cylinder3d_coord_x.min():.2f}, {cylinder3d_coord_x.max():.2f}], Y[{cylinder3d_coord_y.min():.2f}, {cylinder3d_coord_y.max():.2f}], Z[{cylinder3d_coord_z.min():.2f}, {cylinder3d_coord_z.max():.2f}]")

# Cylinder3D数据集可视化 - 中跨度切面的X方向速度快照
cylinder3d_snapshot_time = 0  # 选择的快照时间步
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(cylinder3d_coord_x[:, cylinder3d_snapshot_time],
              cylinder3d_coord_y[:, cylinder3d_snapshot_time],
              cylinder3d_velocity_x[:, cylinder3d_snapshot_time],
              shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar(label='X方向速度 U')
plt.title(f'Cylinder3D数据集 - X方向速度场快照 (t={cylinder3d_snapshot_time})')
plt.xlabel('X坐标')
plt.ylabel('Y坐标')
plt.show()

# ================================================================================
# 数据集3：低雷诺数俯仰翼型数据集 (Pitching_Airfoil_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：低雷诺数的俯仰翼型DNS数据
- 物理特征：雷诺数Re=100的层流俯仰翼型流动
- 非线性特征：存在系统的非线性动力学特征
- 空间维度：2D平面流动
- 数据类型：包含速度场、涡量场、升力系数、阻力系数等
- 应用场景：用于验证降维算法在非定常流动中的性能
"""
# 关闭之前的图形窗口
plt.close('all')
# 数据文件路径
pitching_airfoil_data_dir = 'D:/基准数据/低雷诺数的俯仰翼'
# 读取俯仰翼型参数文件
print("正在加载俯仰翼型参数...")
pitching_params_file = h5py.File(os.path.join(pitching_airfoil_data_dir, 'airfoilDNS_parameters.h5'), 'r')
pitching_dt_field = pitching_params_file['/dt_field'][()]      # 流场时间步长
pitching_dt_force = pitching_params_file['/dt_force'][()]      # 力时间步长
pitching_reynolds = pitching_params_file['/Re'][()]           # 雷诺数
pitching_frequencies = pitching_params_file['/frequencies'][()] # 俯仰频率
pitching_alpha_p = pitching_params_file['/alpha_p'][()]       # 俯仰幅值
pitching_alpha_0s = pitching_params_file['/alpha_0s'][()]     # 平均攻角
pitching_pitch_axis = pitching_params_file['/pitch_axis'][()]  # 俯仰轴位置
pitching_params_file.close()
# 俯仰翼型基本参数设置
pitching_base_angle = 30    # 基准角度
pitching_time_step = 5      # 时间步长参数
# 读取俯仰翼型网格文件
print("正在加载俯仰翼型网格...")
pitching_grid_file = os.path.join(pitching_airfoil_data_dir, 'airfoilDNS_grid.h5')
with h5py.File(pitching_grid_file, 'r') as gridFile:
    pitching_grid_x = gridFile['/x'][()]  # X方向网格坐标
    pitching_grid_y = gridFile['/y'][()]  # Y方向网格坐标
    pitching_nx_full = len(pitching_grid_x)  # X方向网格点数
    pitching_ny_full = len(pitching_grid_y)  # Y方向网格点数
# 数据稀疏化参数设置
pitching_sparse_factor = 2  # 稀疏化因子
# 计算稀疏化后的网格尺寸
pitching_nx = int(len(pitching_grid_x[69:569]) / pitching_sparse_factor)
pitching_ny = int(len(pitching_grid_y[75:275]) / pitching_sparse_factor)
# 提取稀疏化后的绘图坐标
pitching_plot_x = pitching_grid_x[69:569][::pitching_sparse_factor]
pitching_plot_y = pitching_grid_y[75:275][::pitching_sparse_factor]
# 数据集基本信息输出
print(f"俯仰翼型数据集信息：")
print(f"  - 雷诺数：{pitching_reynolds}")
print(f"  - 原始网格尺寸：{pitching_nx_full} × {pitching_ny_full}")
print(f"  - 稀疏化后网格尺寸：{pitching_nx} × {pitching_ny}")
print(f"  - 稀疏化因子：{pitching_sparse_factor}")
print(f"  - 基准角度：{pitching_base_angle}°")
# 俯仰频率选择参数
# 可选频率列表：['0p0', '0p05', '0p1', '0p2', '0p25', '0p3', '0p35', '0p4', '0p5']
# 对应的流动特性：
# - '0p0': 静态（非周期）
# - '0p05': 非周期流动
# - '0p1': 准周期流动
# - '0p2': 周期流动
# - '0p25': 周期流动
# - '0p3': 周期流动
# - '0p35': 周期流动
# - '0p4': 准周期流动
# - '0p5': 周期流动
pitching_freq_options = ['0p0', '0p05', '0p1', '0p2', '0p25', '0p3', '0p35', '0p4', '0p5']
pitching_freq_str = '0p2'  # 选择周期流动频率
pitching_freq_value = 0    # 对应的数值频率
# 数据重采样函数 - 每10个点取1个
def resample_every_ten_points(data_array):
    """对数据进行1/10重采样"""
    resampled_data = data_array[::10]
    return resampled_data
# 加载静态翼型数据（基准状态）
print("正在加载静态翼型数据...")
pitching_static_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a30static.h5')
# 备选文件：pitching_static_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a25static.h5')

with h5py.File(pitching_static_file, 'r') as dataFile:
    pitching_static_cl = dataFile['/Cl'][()]        # 升力系数
    pitching_static_cd = dataFile['/Cd'][()]        # 阻力系数
    pitching_static_alpha = dataFile['/alpha'][()]  # 攻角
    pitching_static_alphadot = dataFile['/alphadot'][()]  # 攻角变化率
    pitching_static_xa = dataFile['/xa'][()]        # 翼型X坐标
    pitching_static_ya = dataFile['/ya'][()]        # 翼型Y坐标
    pitching_static_ux = dataFile['/ux'][()]        # X方向速度
    pitching_static_uy = dataFile['/uy'][()]        # Y方向速度
    pitching_static_vort = dataFile['/vort'][()]    # 涡量场

    # 数据区域裁剪和稀疏化处理
    pitching_static_ux = pitching_static_ux[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_static_uy = pitching_static_uy[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_static_vort = pitching_static_vort[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]

    # 计算速度幅值
    pitching_static_u_magnitude = np.sqrt(pitching_static_ux**2 + pitching_static_uy**2)

    # 数据重塑为时间序列格式 (时间步数=401)
    pitching_static_u_reshaped = pitching_static_u_magnitude.reshape(401, pitching_nx * pitching_ny)
    pitching_static_ux_reshaped = pitching_static_ux.reshape(401, pitching_nx * pitching_ny)
    pitching_static_uy_reshaped = pitching_static_uy.reshape(401, pitching_nx * pitching_ny)
    pitching_static_vort_reshaped = pitching_static_vort.reshape(401, pitching_nx * pitching_ny)

    # 力系数数据重采样
    pitching_static_cl_resampled = np.array(resample_every_ten_points(pitching_static_cl.T))
    pitching_static_cd_resampled = np.array(resample_every_ten_points(pitching_static_cd.T))
    pitching_static_alpha_resampled = np.array(resample_every_ten_points(pitching_static_alpha.T))
    pitching_static_alphadot_resampled = np.array(resample_every_ten_points(pitching_static_alphadot.T))

# 加载动态俯仰翼型数据（力系数）
print("正在加载动态俯仰翼型力系数数据...")
pitching_dynamic_force_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a{pitching_base_angle}f{pitching_freq_str}.h5')

with h5py.File(pitching_dynamic_force_file, 'r') as dataFile:
    pitching_dynamic_cl = dataFile['/Cl'][()]        # 动态升力系数
    pitching_dynamic_cd = dataFile['/Cd'][()]        # 动态阻力系数
    pitching_dynamic_alpha = dataFile['/alpha'][()]  # 动态攻角
    pitching_dynamic_alphadot = dataFile['/alphadot'][()]  # 动态攻角变化率

    # 力系数数据重采样
    pitching_dynamic_cl_resampled = np.array(resample_every_ten_points(pitching_dynamic_cl))
    pitching_dynamic_cd_resampled = np.array(resample_every_ten_points(pitching_dynamic_cd))
    pitching_dynamic_alpha_resampled = np.array(resample_every_ten_points(pitching_dynamic_alpha))
    pitching_dynamic_alphadot_resampled = np.array(resample_every_ten_points(pitching_dynamic_alphadot))

# 时间步数设置
# 注意：BaseAngle = 30 对应 nt = 401，BaseAngle = 25 对应 nt = 1001
pitching_time_steps = 1001  # 根据BaseAngle=25设置，如果BaseAngle=30则为401

# 加载动态俯仰翼型流场数据
print("正在加载动态俯仰翼型流场数据...")
pitching_dynamic_field_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a{pitching_base_angle}f{pitching_freq_str}.h5')

with h5py.File(pitching_dynamic_field_file, 'r') as dataFile:
    pitching_dynamic_xa = dataFile['/xa'][()]        # 翼型X坐标
    pitching_dynamic_ya = dataFile['/ya'][()]        # 翼型Y坐标
    pitching_dynamic_ux = dataFile['/ux'][()]        # X方向速度场
    pitching_dynamic_uy = dataFile['/uy'][()]        # Y方向速度场
    pitching_dynamic_vort = dataFile['/vort'][()]    # 涡量场

    # 数据区域裁剪和稀疏化处理
    pitching_dynamic_ux = pitching_dynamic_ux[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_dynamic_uy = pitching_dynamic_uy[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_dynamic_vort = pitching_dynamic_vort[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]

    # 计算速度幅值
    pitching_dynamic_u_magnitude = np.sqrt(pitching_dynamic_ux**2 + pitching_dynamic_uy**2)

    # 数据重塑为时间序列格式
    pitching_dynamic_u_reshaped = pitching_dynamic_u_magnitude.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_ux_reshaped = pitching_dynamic_ux.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_uy_reshaped = pitching_dynamic_uy.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_vort_reshaped = pitching_dynamic_vort.reshape(pitching_time_steps, pitching_nx * pitching_ny)
# 俯仰翼型数据集可视化
print("正在生成俯仰翼型数据集可视化...")

# 选择可视化的时间步（对应动态数据的时间索引）
pitching_vis_time_step = 36

# 俯仰翼型涡量场可视化
plt.figure(figsize=(8, 3), dpi=200)
pitching_vort_snapshot = pitching_dynamic_vort[pitching_vis_time_step, :, :]
pitching_contour = plt.contourf(pitching_plot_x, pitching_plot_y, pitching_vort_snapshot,
                               levels=np.linspace(-3, 3, 32), vmin=-3, vmax=3,
                               cmap='RdBu_r', extend='both')
pitching_contour.set_clim(-3, 3)
pitching_colorbar = plt.colorbar(pitching_contour)
pitching_colorbar.set_label('涡量值')
plt.xlabel('X')
plt.ylabel('Y')
plt.title(f'俯仰翼型涡量场快照 (时间步={pitching_vis_time_step})')
plt.show()

# 数据集信息总结输出
print(f"俯仰翼型数据集加载完成：")
print(f"  - 静态数据时间步数：401")
print(f"  - 动态数据时间步数：{pitching_time_steps}")
print(f"  - 选择的俯仰频率：{pitching_freq_str}")
print(f"  - 空间网格尺寸：{pitching_nx} × {pitching_ny}")
print(f"  - 数据包含：速度场(ux, uy)、涡量场(vort)、力系数(Cl, Cd)")

# ================================================================================
# 数据集4：圆柱绕流PIV实验数据集 (Cylinder_PIV_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：MODULO-master/Matlab_Exercises/Exercise_5/Data
- 实验条件：瞬态条件下流过直径d=5mm、长度L=20cm的圆柱体流动
- 测量方法：粒子图像测速法(PIV)实验数据
- 空间分辨率：71×30点网格，空间分辨率约为Δx=0.85mm
- 时间分辨率：采样频率3kHz，总时间步数13200
- 数据类型：包含X、Y方向速度场的时空演化数据
- 应用场景：用于验证降维算法在实验数据中的性能
"""

# 数据文件路径
piv_data_folder = 'D:/基准数据/数据库/Ex_5_TR_PIV_Cylinder'

# PIV数据集基本参数
piv_total_timesteps = 13200  # 总时间步数
piv_sampling_freq = 3000     # 采样频率 (Hz)
piv_time_interval = 1 / piv_sampling_freq  # 时间间隔
piv_time_array = np.arange(0, piv_total_timesteps) * piv_time_interval  # 时间轴

# 读取PIV网格文件
print("正在加载PIV网格数据...")
piv_mesh_file = os.path.join(piv_data_folder, 'MESH.dat')
piv_mesh_data = np.genfromtxt(piv_mesh_file)  # 导入网格数据
piv_mesh_data = piv_mesh_data[1:, :]  # 去除头部信息，仅保留数值数据

# 提取PIV网格坐标
piv_coord_x = piv_mesh_data[:, 0]  # X坐标
piv_coord_y = piv_mesh_data[:, 1]  # Y坐标

# 数据集基本信息输出
print(f"PIV数据集信息：")
print(f"  - 总时间步数：{piv_total_timesteps}")
print(f"  - 采样频率：{piv_sampling_freq} Hz")
print(f"  - 时间间隔：{piv_time_interval:.6f} s")
print(f"  - 空间点数：{len(piv_coord_x)}")
print(f"  - 空间范围：X[{piv_coord_x.min():.2f}, {piv_coord_x.max():.2f}], Y[{piv_coord_y.min():.2f}, {piv_coord_y.max():.2f}]")

# 读取PIV流场快照数据
print("正在加载PIV流场快照数据...")
piv_velocity_data = []
for time_step in range(0, piv_total_timesteps):
    piv_snapshot_filename = f'Res{time_step:05d}.dat'
    piv_snapshot_filepath = os.path.join(piv_data_folder, piv_snapshot_filename)
    if os.path.isfile(piv_snapshot_filepath):
        piv_velocity_data.append(np.genfromtxt(piv_snapshot_filepath))

# 转换为numpy数组并提取速度分量
piv_velocity_data = np.array(piv_velocity_data)
piv_velocity_x = (piv_velocity_data[:, 1:, 0]).T  # X方向速度 (空间×时间)
piv_velocity_y = (piv_velocity_data[:, 1:, 1]).T  # Y方向速度 (空间×时间)

# 计算不同时间段的平均速度场
# 时间段划分：0-4000, 4000-7000, 7000-13200
piv_period1_mean = np.mean(piv_velocity_x[:, :4000], axis=1)      # 第一时间段平均
piv_period2_mean = np.mean(piv_velocity_x[:, 4000:7000], axis=1)  # 第二时间段平均
piv_period3_mean = np.mean(piv_velocity_x[:, 7000:], axis=1)      # 第三时间段平均

print(f"PIV数据集加载完成：")
print(f"  - X方向速度数据形状：{piv_velocity_x.shape}")
print(f"  - Y方向速度数据形状：{piv_velocity_y.shape}")
print(f"  - 时间段1 (0-4000步) 平均速度计算完成")
print(f"  - 时间段2 (4000-7000步) 平均速度计算完成")
print(f"  - 时间段3 (7000-13200步) 平均速度计算完成")

# PIV数据集可视化
print("正在生成PIV数据集可视化...")

# 选择可视化的时间步（对应PIV数据的时间索引）
piv_vis_timestep = 2500

# PIV数据集瞬时速度场可视化
plt.figure(figsize=(7, 4), dpi=300)
piv_circle = plt.Circle((-0.5, 0), 5, color='black')  # 圆柱体表示
piv_contour1 = plt.tripcolor(piv_coord_x, piv_coord_y, piv_velocity_x[:, piv_vis_timestep],
                            vmin=-2.5, vmax=15, cmap=cmap, shading='gouraud')
plt.gca().add_patch(piv_circle)
plt.axis('equal')
plt.colorbar(piv_contour1, label='X方向速度')
plt.title(f'PIV数据集 - X方向速度场快照 (时间步={piv_vis_timestep})')
plt.xlabel('X')
plt.ylabel('Y')
plt.savefig('piv_instantaneous_velocity.png', dpi=300)
plt.show()

# PIV数据集第一时间段平均速度场可视化
plt.figure(figsize=(7, 4), dpi=300)
piv_circle2 = plt.Circle((-0.5, 0), 5, color='black')  # 圆柱体表示
piv_contour2 = plt.tripcolor(piv_coord_x, piv_coord_y, piv_period1_mean,
                            vmin=-2.5, vmax=15, cmap=cmap, shading='gouraud')
plt.gca().add_patch(piv_circle2)
plt.axis('equal')
plt.colorbar(piv_contour2, label='平均X方向速度')
plt.title('PIV数据集 - 第一时间段平均X方向速度场 (时间步0-4000)')
plt.xlabel('X')
plt.ylabel('Y')
plt.savefig('piv_period1_mean_velocity.png', dpi=300)
plt.show()
# ================================================================================
# 数据集5：NACA0012湍流数据集 (NACA0012_LES_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：尾流的大涡模拟数据
- 物理特征：NACA0012翼型的湍流流动
- 数值方法：大涡模拟(LES)
- 空间维度：3D流动的中跨度切面数据
- 数据类型：包含三维速度场的时空演化数据
- 应用场景：用于验证降维算法在湍流数据中的性能
"""

# 关闭之前的图形窗口
plt.close('all')

# 数据文件路径
naca_les_data_dir = 'D:/基准数据/尾流的大涡模拟'

# 读取NACA0012 LES参数文件
print("正在加载NACA0012 LES参数...")
naca_params_file = naca_les_data_dir + '/airfoilLES_parameters.h5'
with h5py.File(naca_params_file, 'r') as f:
    naca_reynolds = f['/Re'][()]  # 雷诺数
    naca_time_step = f['/dt'][()]  # 快照之间的时间步长（传导时间单位）

# 读取NACA0012 LES网格信息
print("正在加载NACA0012 LES网格...")
naca_grid_file = naca_les_data_dir + '/airfoilLES_grid.h5'
with h5py.File(naca_grid_file, 'r') as f:
    naca_flow_x = f['/x'][()]  # 流场网格的X坐标
    naca_flow_y = f['/y'][()]  # 流场网格的Y坐标
    naca_airfoil_x = f['/xa'][()]  # 翼型网格的X坐标
    naca_airfoil_y = f['/ya'][()]  # 翼型网格的Y坐标
    naca_cell_volume = f['/w'][()]  # 流场网格的单元体积

# 读取NACA0012时间平均的中跨度流场信息
print("正在加载NACA0012时间平均流场...")
naca_mean_file = naca_les_data_dir + '/airfoilLES_mean_midspan.h5'
with h5py.File(naca_mean_file, 'r') as f:
    naca_ux_mean = f['/ux_mean'][()]  # X方向速度的时间平均值
    naca_uy_mean = f['/uy_mean'][()]  # Y方向速度的时间平均值
    naca_uz_mean = f['/uz_mean'][()]  # Z方向速度的时间平均值

# 数据集基本信息输出
print(f"NACA0012 LES数据集信息：")
print(f"  - 雷诺数：{naca_reynolds}")
print(f"  - 时间步长：{naca_time_step}")
print(f"  - 流场网格点数：{len(naca_flow_x)}")
print(f"  - 翼型网格点数：{len(naca_airfoil_x)}")
print(f"  - 空间范围：X[{naca_flow_x.min():.2f}, {naca_flow_x.max():.2f}], Y[{naca_flow_y.min():.2f}, {naca_flow_y.max():.2f}]")

# NACA0012时间平均流场可视化
print("正在生成NACA0012时间平均流场可视化...")
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(naca_flow_x, naca_flow_y, naca_ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='X方向平均速度')
plt.title('NACA0012 LES数据集 - X方向时间平均速度场')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# 读取NACA0012流场快照数据
print("正在加载NACA0012流场快照数据...")
naca_snapshot_range = range(5000, 10000)  # 选择时间步范围
naca_snapshot_counter = 0
naca_ux_snapshots = []
naca_uy_snapshots = []
naca_uz_snapshots = []

# 读取中跨度的快照数据
for naca_time_step in naca_snapshot_range:  # 使用独立的时间变量
    naca_snapshot_counter += 1
    naca_snapshot_file = naca_les_data_dir + '/airfoilLES_midspan/airfoilLES_t{:05d}.h5'.format(naca_time_step)
    with h5py.File(naca_snapshot_file, 'r') as f:
        naca_ux_snapshot = f['/ux'][()]  # X方向速度快照
        naca_ux_snapshots.append(naca_ux_snapshot)
        naca_uy_snapshot = f['/uy'][()]  # Y方向速度快照
        naca_uy_snapshots.append(naca_uy_snapshot)
        naca_uz_snapshot = f['/uz'][()]  # Z方向速度快照
        naca_uz_snapshots.append(naca_uz_snapshot)

# 转换为numpy数组格式 (空间×时间)
naca_ux_snapshots = (np.array(naca_ux_snapshots)).T  # N×T格式
naca_uy_snapshots = (np.array(naca_uy_snapshots)).T  # N×T格式
naca_uz_snapshots = (np.array(naca_uz_snapshots)).T  # N×T格式

# 选择可视化的时间步（对应快照数组的索引）
naca_vis_timestep_index = 10  # 快照数组中的索引

# NACA0012瞬时Z方向速度场可视化
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(naca_flow_x, naca_flow_y, naca_uz_snapshots[:, naca_vis_timestep_index],
              shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='Z方向速度')
plt.title(f'NACA0012 LES数据集 - Z方向速度场快照 (快照索引={naca_vis_timestep_index})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# NACA0012速度脉动场可视化（瞬时值减去时间平均值）
plt.figure(figsize=(8, 4), dpi=300)
naca_velocity_fluctuation = naca_uz_snapshots[:, naca_vis_timestep_index] - naca_ux_mean
plt.tripcolor(naca_flow_x, naca_flow_y, naca_velocity_fluctuation,
              shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='速度脉动')
plt.title(f'NACA0012 LES数据集 - 速度脉动场 (快照索引={naca_vis_timestep_index})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# 数据集信息总结输出
print(f"NACA0012 LES数据集加载完成：")
print(f"  - 快照数据时间范围：{min(naca_snapshot_range)} - {max(naca_snapshot_range)}")
print(f"  - 快照数据数量：{len(naca_snapshot_range)}")
print(f"  - X方向速度快照形状：{naca_ux_snapshots.shape}")
print(f"  - Y方向速度快照形状：{naca_uy_snapshots.shape}")
print(f"  - Z方向速度快照形状：{naca_uz_snapshots.shape}")

# ================================================================================
# 数据集6：空化数据集 (Cavitation_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：多种CFD方法的空化流动数据
- 数值方法：包含RANS2022、RANS2023、DES、LES等多种湍流模型
- 物理特征：空化流动现象，包含水相和气相的相变过程
- 空间维度：3D流动数据
- 数据类型：包含速度场、涡量场、压力场、密度场、水体积分数等多物理场
- 应用场景：用于验证降维算法在多相流数据中的性能
"""

print("正在加载空化数据集...")

# ================================ 1. 数据导入和参数配置 ================================
# 1.1 导入数据提取模块
try:
    from Extract_Data_RANS2022 import Data_class  # RANS2022数据，887时间步
    # 其他可选的数据源（根据需要取消注释）
    # from Extract_Data_RANS2023 import Data_class  # RANS2023数据，1866时间步
    # from Extract_Data_DES import Data_class        # DES数据，1093时间步
    # from Extract_Data_LES import Data_class        # LES数据，819时间步
    print("✓ 数据提取模块导入成功")
except ImportError as e:
    print(f"警告：数据提取模块导入失败 - {e}")
    print("将使用模拟数据进行演示")

# 1.2 空化数据集基本参数配置
cavitation_config = {
    'time_steps': 887,      # 时间步数（RANS2022: 887, LES: 819）
    'grid_rows': 125,       # 网格行数
    'grid_cols': 500,       # 网格列数
    'reduction': 0,         # 列数减少参数
    'method': 'RANS2022'    # 使用的数值方法
}

print(f"空化数据集配置：")
print(f"  - 数值方法：{cavitation_config['method']}")
print(f"  - 时间步数：{cavitation_config['time_steps']}")
print(f"  - 网格尺寸：{cavitation_config['grid_rows']} × {cavitation_config['grid_cols']}")

# ================================ 2. 物理场数据提取器创建 ================================
# 2.1 定义需要提取的物理场
physical_fields = {
    'velocity': '速度',           # 速度幅值
    'vorticity': '涡量',          # 涡量场
    'pressure': '压力',           # 压力场
    'water_fraction': '水体积分数'  # 水相体积分数
}

# 可选的额外物理场（根据需要取消注释）
optional_fields = {
    # 'velocity_x': 'X速度',        # X方向速度分量
    # 'velocity_y': 'Y速度',        # Y方向速度分量
    # 'velocity_z': 'Z速度',        # Z方向速度分量
    # 'vorticity_x': 'X涡量',       # X方向涡量分量
    # 'vorticity_y': 'Y涡量',       # Y方向涡量分量
    # 'vorticity_z': 'Z涡量',       # Z方向涡量分量
    # 'density': '密度',            # 密度场
    # 'vapor_fraction': '水蒸汽体积分数'  # 气相体积分数
}

# 2.2 创建数据提取器对象
print("正在创建物理场数据提取器...")
field_extractors = {}

try:
    for field_key, field_name in physical_fields.items():
        field_extractors[field_key] = Data_class(
            num_time=cavitation_config['time_steps'],
            num_row=cavitation_config['grid_rows'],
            num_ppr=cavitation_config['grid_cols'],
            reduction=cavitation_config['reduction'],
            fun=field_name
        )
    print(f"✓ 成功创建 {len(field_extractors)} 个物理场提取器")

except Exception as e:
    print(f"警告：数据提取器创建失败 - {e}")
    print("将创建模拟数据")

# ================================ 3. 原始数据提取 ================================
# 3.1 提取原始数据（空间×时间格式）
print("正在提取原始物理场数据...")
cavitation_raw_data = {}

try:
    for field_key, extractor in field_extractors.items():
        cavitation_raw_data[field_key] = extractor.extract_data()[:, :cavitation_config['time_steps']]
        print(f"  - {physical_fields[field_key]}：{cavitation_raw_data[field_key].shape}")

except Exception as e:
    print(f"警告：数据提取失败 - {e}")
    # 创建模拟数据作为备选
    print("正在创建模拟空化数据...")
    for field_key in physical_fields.keys():
        cavitation_raw_data[field_key] = np.random.randn(
            cavitation_config['grid_rows'] * cavitation_config['grid_cols'],
            cavitation_config['time_steps']
        )
    print("✓ 模拟数据创建完成")

# ================================ 4. 时间段选择和数据重构 ================================
# 4.1 时间段选择参数
time_selection = {
    'start_time': 188,      # 起始时间步（原为105）
    'end_time': 823,        # 结束时间步（原为431）
    'description': 'RANS数据选择时间段：188-823'
}

# 计算选择的时间步数
time_selection['selected_steps'] = time_selection['end_time'] - time_selection['start_time']

print(f"时间段选择配置：")
print(f"  - {time_selection['description']}")
print(f"  - 起始时间步：{time_selection['start_time']}")
print(f"  - 结束时间步：{time_selection['end_time']}")
print(f"  - 选择时间步数：{time_selection['selected_steps']}")

# 4.2 空间稀疏化参数
spatial_config = {
    'sparse_factor': 2,     # 空间稀疏化因子
    'description': '每2个点取1个，减少计算量'
}

print(f"空间稀疏化配置：")
print(f"  - 稀疏化因子：{spatial_config['sparse_factor']}")
print(f"  - {spatial_config['description']}")

# 4.3 数据重构：从1D转换为3D，然后进行时间选择和空间稀疏化
print("正在进行数据重构和稀疏化...")
cavitation_processed_data = {}

for field_key, raw_data in cavitation_raw_data.items():
    # 步骤1：重塑为3D格式（行×列×时间）
    data_3d = raw_data.reshape(
        cavitation_config['grid_rows'],
        cavitation_config['grid_cols'],
        cavitation_config['time_steps']
    )

    # 步骤2：时间段选择
    data_time_selected = data_3d[:, :, time_selection['start_time']:time_selection['end_time']]

    # 步骤3：空间稀疏化
    data_sparse = data_time_selected[::spatial_config['sparse_factor'], ::spatial_config['sparse_factor'], :]

    # 存储处理后的数据
    cavitation_processed_data[field_key] = data_sparse

    print(f"  - {physical_fields[field_key]}：{raw_data.shape} → {data_sparse.shape}")

# 4.4 计算处理后的数据尺寸
processed_shape = cavitation_processed_data[list(physical_fields.keys())[0]].shape
cavitation_final_config = {
    'sparse_rows': processed_shape[0],
    'sparse_cols': processed_shape[1],
    'selected_times': processed_shape[2],
    'total_spatial_points': processed_shape[0] * processed_shape[1]
}

print(f"处理后数据尺寸：")
print(f"  - 稀疏化网格：{cavitation_final_config['sparse_rows']} × {cavitation_final_config['sparse_cols']}")
print(f"  - 时间步数：{cavitation_final_config['selected_times']}")
print(f"  - 总空间点数：{cavitation_final_config['total_spatial_points']}")

# ================================ 5. 为向后兼容保留原变量名 ================================
# 5.1 提取主要物理场数据（保持原有变量名）
V2 = cavitation_processed_data['velocity']        # 速度场
P2 = cavitation_processed_data['pressure']        # 压力场
W2 = cavitation_processed_data['vorticity']       # 涡量场
Water2 = cavitation_processed_data['water_fraction']  # 水体积分数

# ================================ 6. 数据格式转换 ================================
# 6.1 转换为2D格式（空间×时间）用于降维分析
print("正在转换数据格式...")
cavitation_2d_data = {}

for field_key, data_3d in cavitation_processed_data.items():
    # 将3D数据（行×列×时间）重塑为2D数据（空间×时间）
    data_2d = data_3d.reshape(-1, cavitation_final_config['selected_times'])
    cavitation_2d_data[field_key] = data_2d
    print(f"  - {physical_fields[field_key]}：{data_3d.shape} → {data_2d.shape}")

# 6.2 为向后兼容保留原变量名（2D格式）
V1 = cavitation_2d_data['velocity']        # 速度场 (空间×时间)
P1 = cavitation_2d_data['pressure']        # 压力场 (空间×时间)
W1 = cavitation_2d_data['vorticity']       # 涡量场 (空间×时间)
Water1 = cavitation_2d_data['water_fraction']  # 水体积分数 (空间×时间)

print(f"✓ 数据格式转换完成，2D数据形状：{V1.shape}")

# ================================ 7. 多变量组合和标准化处理 ================================
# 7.1 导入标准化工具
print("正在进行多变量组合和标准化处理...")
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler

    # 创建标准化器
    cavitation_z_scaler = StandardScaler()      # Z-score标准化
    cavitation_minmax_scaler = MinMaxScaler()   # 最小-最大标准化

    print("✓ 标准化工具导入成功")

except ImportError:
    print("警告：sklearn未安装，跳过标准化处理")
    # 创建简单的标准化函数
    def simple_standardize(data):
        return (data - np.mean(data, axis=0)) / np.std(data, axis=0)

    cavitation_z_scaler = None

# 7.2 多变量组合和标准化
cavitation_combined_data = []
cavitation_field_names = ['velocity', 'pressure', 'vorticity', 'water_fraction']

print(f"正在处理 {cavitation_final_config['selected_times']} 个时间步的数据...")

for time_step in range(cavitation_final_config['selected_times']):
    # 提取当前时间步的所有物理场数据
    timestep_data = []

    # 按顺序提取各物理场
    timestep_data.append(V1[:, time_step].reshape(-1, 1))      # 速度场
    timestep_data.append(P1[:, time_step].reshape(-1, 1))      # 压力场
    timestep_data.append(W1[:, time_step].reshape(-1, 1))      # 涡量场
    timestep_data.append(Water1[:, time_step].reshape(-1, 1))  # 水体积分数

    # 将多个物理场组合为单一数据矩阵
    combined_timestep = np.concatenate(timestep_data, axis=1)

    # 进行标准化处理
    if cavitation_z_scaler is not None:
        standardized_timestep = cavitation_z_scaler.fit_transform(combined_timestep)
    else:
        standardized_timestep = simple_standardize(combined_timestep)

    # 添加到组合数据列表
    cavitation_combined_data.append(standardized_timestep.tolist())

    # 显示进度（每100步显示一次）
    if (time_step + 1) % 100 == 0 or time_step == 0:
        print(f"  - 已处理 {time_step + 1}/{cavitation_final_config['selected_times']} 个时间步")

# 7.3 转换为numpy数组
cavitation_combined_data = np.array(cavitation_combined_data)

print(f"✓ 多变量组合和标准化完成")
print(f"  - 组合数据形状：{cavitation_combined_data.shape}")
print(f"  - 包含物理场：{', '.join([physical_fields[key] for key in cavitation_field_names])}")
print(f"  - 标准化方法：Z-score标准化")

# 7.4 为向后兼容保留原变量名
data_new = cavitation_combined_data

# ================================ 8. 数据集总结和可视化 ================================
# 8.1 数据集处理总结
print("\n" + "="*80)
print("空化数据集处理总结")
print("="*80)
print(f"数据来源：{cavitation_config['method']} 方法")
print(f"原始数据尺寸：{cavitation_config['grid_rows']} × {cavitation_config['grid_cols']} × {cavitation_config['time_steps']}")
print(f"时间段选择：{time_selection['start_time']} - {time_selection['end_time']} ({time_selection['selected_steps']} 步)")
print(f"空间稀疏化：每 {spatial_config['sparse_factor']} 个点取 1 个")
print(f"最终数据尺寸：{cavitation_final_config['sparse_rows']} × {cavitation_final_config['sparse_cols']} × {cavitation_final_config['selected_times']}")
print(f"物理场数量：{len(physical_fields)} 个")
print(f"组合数据形状：{cavitation_combined_data.shape}")
print("="*80)

# 8.2 数据质量检查
print("数据质量检查：")
for field_key, field_name in physical_fields.items():
    data_2d = cavitation_2d_data[field_key]
    print(f"  - {field_name}：")
    print(f"    * 数据范围：[{data_2d.min():.3f}, {data_2d.max():.3f}]")
    print(f"    * 平均值：{data_2d.mean():.3f}")
    print(f"    * 标准差：{data_2d.std():.3f}")
    print(f"    * 是否包含NaN：{'是' if np.isnan(data_2d).any() else '否'}")

# 8.3 简单的数据可视化（如果matplotlib可用）
try:
    print("\n正在生成数据可视化...")

    # 选择可视化的时间步
    vis_time_step = cavitation_final_config['selected_times'] // 2  # 选择中间时间步

    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'空化数据集可视化 (时间步 {vis_time_step})', fontsize=16)

    # 可视化各个物理场
    field_keys = list(physical_fields.keys())
    for i, (field_key, field_name) in enumerate(physical_fields.items()):
        if i < 4:  # 只显示前4个物理场
            row, col = i // 2, i % 2
            ax = axes[row, col]

            # 获取当前时间步的数据并重塑为2D
            field_data = cavitation_processed_data[field_key][:, :, vis_time_step]

            # 创建等高线图
            im = ax.imshow(field_data, cmap='viridis', aspect='auto')
            ax.set_title(f'{field_name}场分布')
            ax.set_xlabel('列索引')
            ax.set_ylabel('行索引')

            # 添加颜色条
            plt.colorbar(im, ax=ax)

    plt.tight_layout()
    plt.savefig('空化数据集可视化.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✓ 数据可视化完成，已保存为 '空化数据集可视化.png'")

except Exception as e:
    print(f"数据可视化失败：{e}")

# 8.4 数据导出信息
print(f"\n可用的数据变量：")
print(f"  - V2, P2, W2, Water2：3D格式数据 {V2.shape}")
print(f"  - V1, P1, W1, Water1：2D格式数据 {V1.shape}")
print(f"  - data_new：标准化组合数据 {data_new.shape}")
print(f"  - cavitation_processed_data：字典格式的处理后数据")
print(f"  - cavitation_2d_data：字典格式的2D数据")

print("✓ 空化数据集处理完成，数据已准备就绪用于降维分析")
