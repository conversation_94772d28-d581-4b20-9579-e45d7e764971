#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用流形解码器 (Universal Manifold Decoder)

基于流形图结构的统一解码器，能够处理多种流形学习方法的嵌入结果，
包括 LLE、Isomap、Diffusion Maps 等。

理论基础：
- 局部一致性定理：解码器拟合局部嵌入反函数的性质
- 同一高维流形假设：多个嵌入方法源自同一流形
- 通用逼近定理：神经网络能够逼近任何连续函数

作者：基于您提供的理论框架实现
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.neighbors import NearestNeighbors, kneighbors_graph
from sklearn.manifold import LocallyLinearEmbedding, Isomap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error
from typing import Dict, List, Tuple, Optional, Union
import warnings
import time

# ================================ 核心算法部分 ================================

class ManifoldGraphStructure:
    """
    流形图结构类 - 实现共性结构抽象

    基于理论框架：G(z) := Graph(z, N(z), w_ij)

    两类共性结构：
    1. 邻居集合 N(z)：反映流形上的局部几何结构
    2. 几何结构 G(z)：包括权重图、测地距离图、扩散核等

    支持的权重类型：
    - 欧氏距离权重
    - 高斯核权重
    - 归一化拉普拉斯权重
    - LLE局部线性权重
    - Isomap测地距离权重
    """

    def __init__(self, k_neighbors: int = 10, weight_mode: str = 'gaussian'):
        """
        初始化流形图结构

        参数:
            k_neighbors: k近邻数量，用于构建邻居集合 N(z)
            weight_mode: 权重计算模式，用于构建几何结构 G(z)
                       ('euclidean', 'gaussian', 'lle', 'isomap', 'laplacian')
        """
        self.k_neighbors = k_neighbors
        self.weight_mode = weight_mode
        self.nbrs = None

        # 存储共性结构组件
        self.neighbor_sets = None      # N(z): 邻居集合
        self.geometric_structure = None # G(z): 几何结构
        self.weight_matrix = None      # w_ij: 权重矩阵
        
    def build_neighbor_sets(self, Z: np.ndarray) -> np.ndarray:
        """
        构建邻居集合 N(z) - 第一类共性结构

        反映流形上的局部几何结构，所有流形学习方法都基于此构建连接

        参数:
            Z: 低维嵌入数据 (n_samples, d)

        返回:
            neighbors: 邻居索引矩阵 (n_samples, k_neighbors)
        """
        # 构建k近邻图 - 这是所有方法的共性
        self.nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1)
        self.nbrs.fit(Z)

        # 获取邻居索引（移除自身）
        distances, neighbors = self.nbrs.kneighbors(Z)
        self.neighbor_sets = neighbors[:, 1:]  # N(z)

        return self.neighbor_sets, distances[:, 1:]

    def build_geometric_structure(self, Z: np.ndarray, distances: np.ndarray,
                                sigma: float = 1.0) -> np.ndarray:
        """
        构建几何结构 G(z) - 第二类共性结构

        包括权重图（LLE）、测地距离图（Isomap）、扩散核（Diffusion Maps）等

        参数:
            Z: 低维嵌入数据 (n_samples, d)
            distances: 邻居距离矩阵 (n_samples, k_neighbors)
            sigma: 高斯核参数

        返回:
            weights: 权重矩阵 (n_samples, k_neighbors)
        """
        n_samples = Z.shape[0]

        if self.weight_mode == 'euclidean':
            # 欧氏距离权重 - 距离越小权重越大
            weights = 1.0 / (distances + 1e-8)

        elif self.weight_mode == 'gaussian':
            # 高斯核权重 - 扩散映射类型
            weights = np.exp(-distances**2 / (2 * sigma**2))

        elif self.weight_mode == 'lle':
            # LLE局部线性权重
            weights = self._compute_lle_weights(Z, self.neighbor_sets)

        elif self.weight_mode == 'isomap':
            # Isomap测地距离权重
            weights = self._compute_isomap_weights(Z, self.neighbor_sets, distances)

        elif self.weight_mode == 'laplacian':
            # 归一化拉普拉斯权重
            weights = self._compute_laplacian_weights(distances, sigma)

        else:
            raise ValueError(f"Unknown weight_mode: {self.weight_mode}")

        # 归一化权重 - 这是所有方法的共性
        weights = weights / np.sum(weights, axis=1, keepdims=True)
        self.weight_matrix = weights

        return weights

    def build_graph(self, Z: np.ndarray, sigma: float = 1.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        构建完整的流形图结构 G(z) := Graph(z, N(z), w_ij)

        整合两类共性结构：
        1. 邻居集合 N(z)：局部几何结构
        2. 几何结构 G(z)：权重计算

        参数:
            Z: 低维嵌入数据 (n_samples, d)
            sigma: 高斯核参数

        返回:
            neighbors: 邻居索引矩阵 (n_samples, k_neighbors)
            weights: 权重矩阵 (n_samples, k_neighbors)
        """
        # 第一步：构建邻居集合 N(z)
        neighbors, distances = self.build_neighbor_sets(Z)

        # 第二步：构建几何结构 G(z)
        weights = self.build_geometric_structure(Z, distances, sigma)

        # 存储完整的图结构
        self.geometric_structure = {
            'z': Z,                    # 嵌入点
            'N_z': neighbors,          # 邻居集合 N(z)
            'w_ij': weights,           # 权重 w_ij
            'graph_type': self.weight_mode,
            'structure_formula': 'G(z) := Graph(z, N(z), w_ij)'
        }

        return neighbors, weights

    def _compute_lle_weights(self, Z: np.ndarray, neighbors: np.ndarray) -> np.ndarray:
        """
        计算LLE局部线性权重 - 权重图类型

        求解局部线性重构问题：min ||z_i - Σw_ij*z_j||²
        约束：Σw_ij = 1
        """
        n_samples = Z.shape[0]
        weights = np.zeros((n_samples, self.k_neighbors))

        for i in range(n_samples):
            neighbor_indices = neighbors[i]
            Zi = Z[neighbor_indices] - Z[i]  # 中心化邻居

            # 局部协方差矩阵
            C = Zi @ Zi.T

            # 正则化避免奇异
            reg = 1e-3 * np.trace(C) if np.trace(C) > 0 else 1e-3
            C += reg * np.eye(self.k_neighbors)

            # 求解权重：Cw = 1, sum(w) = 1
            w = np.linalg.solve(C, np.ones(self.k_neighbors))
            w = w / np.sum(w)  # 归一化

            weights[i] = w

        return weights

    def _compute_isomap_weights(self, Z: np.ndarray, neighbors: np.ndarray,
                              distances: np.ndarray) -> np.ndarray:
        """
        计算Isomap测地距离权重 - 测地距离图类型

        基于测地距离构建权重：w_ij = f(d_geodesic(i,j))
        """
        n_samples = Z.shape[0]

        # 构建邻接矩阵
        adj_matrix = np.full((n_samples, n_samples), np.inf)
        np.fill_diagonal(adj_matrix, 0)

        for i in range(n_samples):
            neighbor_indices = neighbors[i]
            neighbor_distances = distances[i]
            adj_matrix[i, neighbor_indices] = neighbor_distances
            adj_matrix[neighbor_indices, i] = neighbor_distances

        # Floyd-Warshall算法计算测地距离
        geodesic_dist = adj_matrix.copy()
        for k in range(n_samples):
            for i in range(n_samples):
                for j in range(n_samples):
                    if geodesic_dist[i, k] + geodesic_dist[k, j] < geodesic_dist[i, j]:
                        geodesic_dist[i, j] = geodesic_dist[i, k] + geodesic_dist[k, j]

        # 基于测地距离计算权重
        weights = np.zeros((n_samples, self.k_neighbors))
        for i in range(n_samples):
            neighbor_indices = neighbors[i]
            geo_dists = geodesic_dist[i, neighbor_indices]
            weights[i] = 1.0 / (geo_dists + 1e-8)

        return weights

    def _compute_laplacian_weights(self, distances: np.ndarray, sigma: float) -> np.ndarray:
        """
        计算归一化拉普拉斯权重 - 扩散核类型

        基于高斯核的归一化拉普拉斯权重
        """
        # 高斯核
        weights = np.exp(-distances**2 / (2 * sigma**2))
        return weights

    def get_structure_info(self) -> Dict:
        """
        获取流形图结构的详细信息

        返回完整的 G(z) := Graph(z, N(z), w_ij) 结构信息
        """
        if self.geometric_structure is None:
            raise ValueError("图结构尚未构建，请先调用 build_graph() 方法")

        structure_info = {
            'theoretical_form': 'G(z) := Graph(z, N(z), w_ij)',
            'components': {
                'z': '低维嵌入点',
                'N(z)': '邻居集合 - 反映局部几何结构',
                'w_ij': '边权重 - 编码几何关系'
            },
            'implementation': {
                'neighbor_method': f'k-NN with k={self.k_neighbors}',
                'weight_type': self.weight_mode,
                'graph_sparsity': np.sum(self.weight_matrix > 0) / self.weight_matrix.size,
                'weight_statistics': {
                    'mean': np.mean(self.weight_matrix[self.weight_matrix > 0]),
                    'std': np.std(self.weight_matrix[self.weight_matrix > 0]),
                    'min': np.min(self.weight_matrix[self.weight_matrix > 0]),
                    'max': np.max(self.weight_matrix[self.weight_matrix > 0])
                }
            },
            'geometric_structure': self.geometric_structure
        }

        return structure_info

    def visualize_manifold_graph(self, Z: np.ndarray = None, method_name: str = None,
                                save_path: str = None) -> Dict:
        """
        可视化流形图结构 G(z) := Graph(z, N(z), w_ij)

        参数:
            Z: 低维嵌入数据（如果为None，使用存储的数据）
            method_name: 方法名称
            save_path: 保存路径

        返回:
            visualization_info: 可视化信息
        """
        if self.geometric_structure is None:
            raise ValueError("图结构尚未构建，请先调用 build_graph() 方法")

        # 使用存储的数据或提供的数据
        if Z is None:
            Z = self.geometric_structure['z']
        neighbors = self.geometric_structure['N_z']
        weights = self.geometric_structure['w_ij']

        if method_name is None:
            method_name = f"{self.weight_mode.upper()} Graph"

        # 创建可视化器
        visualizer = ManifoldGraphVisualizer()

        # 生成可视化
        viz_info = visualizer.visualize_graph_structure(
            Z, neighbors, weights, method_name, save_path
        )

        return viz_info

    def get_neighbor_features(self, Z: np.ndarray, neighbors: np.ndarray,
                            weights: np.ndarray) -> np.ndarray:
        """
        获取邻居聚合特征
        
        参数:
            Z: 低维嵌入数据
            neighbors: 邻居索引
            weights: 权重矩阵
            
        返回:
            neighbor_features: 邻居聚合特征 (n_samples, d)
        """
        n_samples, d = Z.shape
        neighbor_features = np.zeros((n_samples, d))
        
        for i in range(n_samples):
            # 邻居聚合：h = Σ w_j * z_j
            neighbor_coords = Z[neighbors[i]]  # (k_neighbors, d)
            neighbor_weights = weights[i].reshape(-1, 1)  # (k_neighbors, 1)
            neighbor_features[i] = np.sum(neighbor_weights * neighbor_coords, axis=0)
        
        return neighbor_features




class UniversalManifoldDecoder(nn.Module):
    """
    通用流形解码器
    
    基于流形图结构的神经网络解码器：
    Ψ(z) = f(z, G(z)) → R^D
    """
    
    def __init__(self, input_dim: int, output_dim: int, hidden_dims: List[int] = [128, 256, 128],
                 dropout_rate: float = 0.1):
        """
        初始化解码器
        
        参数:
            input_dim: 输入维度 (低维嵌入维度 d)
            output_dim: 输出维度 (高维原始数据维度 D)
            hidden_dims: 隐藏层维度列表
            dropout_rate: Dropout比率
        """
        super(UniversalManifoldDecoder, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 构建网络层
        layers = []
        prev_dim = input_dim * 2  # z 和 h 的拼接
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim),  # 使用LayerNorm替代BatchNorm1d
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.decoder = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, z: torch.Tensor, h: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        参数:
            z: 低维嵌入 (batch_size, d)
            h: 邻居聚合特征 (batch_size, d)
            
        返回:
            x_reconstructed: 重构的高维数据 (batch_size, D)
        """
        # 拼接输入：concat(z, h)
        input_features = torch.cat([z, h], dim=1)
        
        # 解码器网络
        x_reconstructed = self.decoder(input_features)
        
        return x_reconstructed


class UniversalManifoldSystem:
    """
    通用流形解码系统 - 基于共性结构抽象

    实现理论框架：G(z) := Graph(z, N(z), w_ij)

    核心思想：
    所有流形学习方法本质上都构建了两类共性结构：
    1. 邻居集合 N(z)：反映流形上的局部几何结构
    2. 几何结构 G(z)：包括权重图（LLE）、测地距离图（Isomap）、扩散核（Diffusion Maps）

    统一抽象：
    - z: 低维嵌入点
    - N(z): k近邻邻居集合
    - w_ij: 边权重（欧氏距离、高斯核、归一化拉普拉斯权重等）

    解码器形式：
    Ψ(z) = f(z, G(z)) → R^D
    """
    
    def __init__(self, embedding_dim: int, original_dim: int, k_neighbors: int = 10,
                 weight_mode: str = 'gaussian', hidden_dims: List[int] = [128, 256, 128]):
        """
        初始化系统
        
        参数:
            embedding_dim: 嵌入维度 d
            original_dim: 原始数据维度 D
            k_neighbors: k近邻数量
            weight_mode: 权重计算模式
            hidden_dims: 解码器隐藏层维度
        """
        self.embedding_dim = embedding_dim
        self.original_dim = original_dim
        
        # 初始化图结构
        self.graph_structure = ManifoldGraphStructure(k_neighbors, weight_mode)
        
        # 初始化解码器
        self.decoder = UniversalManifoldDecoder(
            embedding_dim, original_dim, hidden_dims
        )
        
        # 训练相关
        self.optimizer = None
        self.criterion = nn.MSELoss()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.decoder.to(self.device)
        
        # 存储训练数据的图结构
        self.train_neighbors = None
        self.train_weights = None
        self.train_Z = None
    
    def fit(self, Z: np.ndarray, X: np.ndarray, epochs: int = 1000,
            learning_rate: float = 0.001, batch_size: int = 64,
            validation_split: float = 0.2, early_stopping_patience: int = 100,
            min_delta: float = 1e-6, verbose: bool = True) -> Dict:
        """
        训练解码器（支持训练/验证集分离和早停）

        参数:
            Z: 低维嵌入数据 (n_samples, d)
            X: 原始高维数据 (n_samples, D)
            epochs: 训练轮数
            learning_rate: 学习率
            batch_size: 批次大小
            validation_split: 验证集比例 (0.0-1.0)
            early_stopping_patience: 早停耐心值
            min_delta: 最小改进阈值
            verbose: 是否显示训练过程

        返回:
            training_history: 训练历史（包含早停信息）
        """
        # 数据预处理
        Z = Z.astype(np.float32)
        X = X.astype(np.float32)
        
        # 构建图结构
        if verbose:
            print("构建流形图结构...")
        neighbors, weights = self.graph_structure.build_graph(Z)
        neighbor_features = self.graph_structure.get_neighbor_features(Z, neighbors, weights)
        
        # 存储训练数据的图结构
        self.train_neighbors = neighbors
        self.train_weights = weights
        self.train_Z = Z
        
        # 数据划分
        n_samples = Z.shape[0]
        if validation_split > 0:
            n_val = int(n_samples * validation_split)
            indices = np.random.permutation(n_samples)

            train_idx = indices[n_val:]
            val_idx = indices[:n_val]

            Z_train, Z_val = Z[train_idx], Z[val_idx]
            X_train, X_val = X[train_idx], X[val_idx]
            h_train, h_val = neighbor_features[train_idx], neighbor_features[val_idx]
        else:
            # 无验证集，使用全部数据作为训练集
            train_idx = np.arange(n_samples)
            val_idx = np.array([])

            Z_train, Z_val = Z, np.empty((0, Z.shape[1]))
            X_train, X_val = X, np.empty((0, X.shape[1]))
            h_train, h_val = neighbor_features, np.empty((0, neighbor_features.shape[1]))
        
        # 转换为PyTorch张量
        Z_train_tensor = torch.FloatTensor(Z_train).to(self.device)
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        h_train_tensor = torch.FloatTensor(h_train).to(self.device)

        if validation_split > 0:
            Z_val_tensor = torch.FloatTensor(Z_val).to(self.device)
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            h_val_tensor = torch.FloatTensor(h_val).to(self.device)
        else:
            Z_val_tensor = None
            X_val_tensor = None
            h_val_tensor = None
        
        # 初始化优化器
        self.optimizer = optim.Adam(self.decoder.parameters(), lr=learning_rate)

        # 创建学习率调度器（兼容不同PyTorch版本）
        try:
            # 尝试使用verbose参数（旧版本PyTorch）
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=50, 
            )
        except TypeError:
            # 如果verbose参数不支持，使用不带verbose的版本（新版本PyTorch）
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=50
            )
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'reconstruction_error': [],
            'best_epoch': 0,
            'best_val_loss': float('inf'),
            'early_stopped': False,
            'final_epoch': epochs
        }

        # 早停相关变量
        patience_counter = 0
        best_model_state = None

        if verbose:
            print(f"开始训练解码器...")
            print(f"训练样本: {len(train_idx)}, 验证样本: {len(val_idx)}")
            print(f"验证集比例: {validation_split:.1%}")
            if validation_split > 0:
                print(f"早停耐心值: {early_stopping_patience}")

        # 训练循环
        for epoch in range(epochs):
            # 训练模式
            self.decoder.train()
            
            # 批次训练
            train_loss = 0.0
            n_batches = (len(train_idx) + batch_size - 1) // batch_size
            
            for i in range(n_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(train_idx))
                
                batch_Z = Z_train_tensor[start_idx:end_idx]
                batch_X = X_train_tensor[start_idx:end_idx]
                batch_h = h_train_tensor[start_idx:end_idx]
                
                # 前向传播
                self.optimizer.zero_grad()
                X_pred = self.decoder(batch_Z, batch_h)
                loss = self.criterion(X_pred, batch_X)
                
                # 反向传播
                loss.backward()
                self.optimizer.step()
                
                train_loss += loss.item()
            
            train_loss /= n_batches
            
            # 验证
            if validation_split > 0:
                self.decoder.eval()
                with torch.no_grad():
                    X_val_pred = self.decoder(Z_val_tensor, h_val_tensor)
                    val_loss = self.criterion(X_val_pred, X_val_tensor).item()

                    # 计算重构误差
                    reconstruction_error = mean_squared_error(
                        X_val_tensor.cpu().numpy(),
                        X_val_pred.cpu().numpy()
                    )
            else:
                # 无验证集时使用训练损失
                val_loss = train_loss
                reconstruction_error = train_loss
            
            # 更新学习率
            scheduler.step(val_loss)

            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['reconstruction_error'].append(reconstruction_error)

            # 早停检查（仅在有验证集时）
            if validation_split > 0:
                improvement = history['best_val_loss'] - val_loss
                if improvement > min_delta:
                    # 验证损失有显著改善
                    history['best_val_loss'] = val_loss
                    history['best_epoch'] = epoch
                    patience_counter = 0
                    # 保存最佳模型状态
                    best_model_state = {
                        'decoder_state': self.decoder.state_dict().copy(),
                        'optimizer_state': self.optimizer.state_dict().copy()
                    }
                else:
                    # 验证损失没有改善
                    patience_counter += 1

                # 检查是否触发早停
                if patience_counter >= early_stopping_patience:
                    history['early_stopped'] = True
                    history['final_epoch'] = epoch + 1
                    if verbose:
                        print(f"\n早停触发! 在第{epoch+1}轮停止训练")
                        print(f"最佳验证损失: {history['best_val_loss']:.6f} (第{history['best_epoch']+1}轮)")
                    break

            # 打印进度
            if verbose and (epoch + 1) % 100 == 0:
                if validation_split > 0:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Train Loss: {train_loss:.6f}, "
                          f"Val Loss: {val_loss:.6f}, "
                          f"Best Val Loss: {history['best_val_loss']:.6f}, "
                          f"Patience: {patience_counter}/{early_stopping_patience}")
                else:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Train Loss: {train_loss:.6f}, "
                          f"Reconstruction Error: {reconstruction_error:.6f}")

        # 恢复最佳模型（如果有验证集且找到了更好的模型）
        if validation_split > 0 and best_model_state is not None:
            self.decoder.load_state_dict(best_model_state['decoder_state'])
            self.optimizer.load_state_dict(best_model_state['optimizer_state'])
            if verbose:
                print(f"已恢复第{history['best_epoch']+1}轮的最佳模型")

        if verbose:
            if history['early_stopped']:
                print(f"训练完成! (早停于第{history['final_epoch']}轮)")
            else:
                print("训练完成!")

            if validation_split > 0:
                print(f"最终验证损失: {history['val_loss'][-1]:.6f}")
                print(f"最佳验证损失: {history['best_val_loss']:.6f}")

        return history

    def split_train_validation(self, Z: np.ndarray, X: np.ndarray,
                              validation_split: float = 0.2,
                              random_state: int = None) -> Tuple:
        """
        分离训练集和验证集

        参数:
            Z: 低维嵌入数据
            X: 原始高维数据
            validation_split: 验证集比例
            random_state: 随机种子

        返回:
            (Z_train, X_train, Z_val, X_val, train_indices, val_indices)
        """
        if random_state is not None:
            np.random.seed(random_state)

        n_samples = len(Z)
        n_val = int(n_samples * validation_split)

        # 随机打乱索引
        indices = np.random.permutation(n_samples)

        if validation_split > 0:
            val_indices = indices[:n_val]
            train_indices = indices[n_val:]

            Z_train, X_train = Z[train_indices], X[train_indices]
            Z_val, X_val = Z[val_indices], X[val_indices]
        else:
            # 无验证集
            train_indices = indices
            val_indices = np.array([])
            Z_train, X_train = Z, X
            Z_val, X_val = None, None

        return Z_train, X_train, Z_val, X_val, train_indices, val_indices

    def evaluate_on_validation(self, Z_val: np.ndarray, X_val: np.ndarray) -> Dict:
        """
        在验证集上评估模型

        参数:
            Z_val: 验证集嵌入数据
            X_val: 验证集原始数据

        返回:
            validation_metrics: 验证指标
        """
        if Z_val is None or X_val is None:
            return {'error': 'No validation data provided'}

        # 解码验证集
        X_val_pred = self.decode(Z_val)

        # 计算验证指标
        metrics = {
            'val_mse': mean_squared_error(X_val, X_val_pred),
            'val_mae': np.mean(np.abs(X_val - X_val_pred)),
            'val_correlation': np.corrcoef(X_val.flatten(), X_val_pred.flatten())[0, 1],
            'val_relative_error': np.mean(np.abs(X_val - X_val_pred) / (np.abs(X_val) + 1e-8))
        }

        return metrics

    def decode(self, Z: np.ndarray) -> np.ndarray:
        """
        解码低维嵌入为高维数据
        
        参数:
            Z: 低维嵌入数据 (n_samples, d)
            
        返回:
            X_reconstructed: 重构的高维数据 (n_samples, D)
        """
        if self.train_Z is None:
            raise ValueError("模型尚未训练，请先调用 fit() 方法")
        
        # 构建图结构（基于训练数据）
        neighbors, weights = self._build_test_graph(Z)
        neighbor_features = self.graph_structure.get_neighbor_features(Z, neighbors, weights)
        
        # 转换为张量
        Z_tensor = torch.FloatTensor(Z).to(self.device)
        h_tensor = torch.FloatTensor(neighbor_features).to(self.device)
        
        # 解码
        self.decoder.eval()
        with torch.no_grad():
            X_reconstructed = self.decoder(Z_tensor, h_tensor)
        
        return X_reconstructed.cpu().numpy()
    
    def _build_test_graph(self, Z_test: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        为测试数据构建图结构（基于训练数据的邻居）
        
        参数:
            Z_test: 测试嵌入数据
            
        返回:
            neighbors: 邻居索引
            weights: 权重矩阵
        """
        # 使用训练时的邻居搜索器
        distances, neighbors = self.graph_structure.nbrs.kneighbors(Z_test)
        
        # 移除自身（如果存在）
        if neighbors.shape[1] > self.graph_structure.k_neighbors:
            neighbors = neighbors[:, 1:]
            distances = distances[:, 1:]
        
        # 计算权重
        if self.graph_structure.weight_mode == 'distance':
            weights = 1.0 / (distances + 1e-8)
        elif self.graph_structure.weight_mode == 'gaussian':
            weights = np.exp(-distances**2 / 2.0)
        else:  # uniform
            weights = np.ones_like(distances)
        
        # 归一化权重
        weights = weights / np.sum(weights, axis=1, keepdims=True)
        
        return neighbors, weights
    
    def evaluate(self, Z: np.ndarray, X_true: np.ndarray) -> Dict[str, float]:
        """
        评估解码器性能
        
        参数:
            Z: 低维嵌入数据
            X_true: 真实高维数据
            
        返回:
            metrics: 评估指标字典
        """
        X_pred = self.decode(Z)
        
        # 计算各种误差指标
        mse = mean_squared_error(X_true, X_pred)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(X_true - X_pred))
        
        # 相对误差
        relative_error = np.mean(np.abs(X_true - X_pred) / (np.abs(X_true) + 1e-8))
        
        # 相关系数
        correlation = np.corrcoef(X_true.flatten(), X_pred.flatten())[0, 1]

        # Frobenius范数误差（相对误差）
        frobenius_error = np.linalg.norm(X_true - X_pred) / np.linalg.norm(X_true)

        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'relative_error': relative_error,
            'correlation': correlation,
            'frobenius_error': frobenius_error
        }

    def analyze_common_structure(self, Z: np.ndarray) -> Dict:
        """
        分析和验证共性结构抽象

        验证 G(z) := Graph(z, N(z), w_ij) 的两类共性结构：
        1. 邻居集合 N(z) 的局部几何特性
        2. 几何结构 G(z) 的权重特性

        参数:
            Z: 低维嵌入数据

        返回:
            analysis: 共性结构分析结果
        """
        if self.train_Z is None:
            raise ValueError("系统尚未训练，请先调用 fit() 方法")

        # 构建图结构
        neighbors, weights = self.graph_structure.build_graph(Z)
        structure_info = self.graph_structure.get_structure_info()

        # 分析邻居集合 N(z) 的特性
        neighbor_analysis = {
            'avg_neighbors': self.graph_structure.k_neighbors,
            'neighbor_consistency': self._compute_neighbor_consistency(neighbors),
            'local_density': self._compute_local_density(Z, neighbors),
            'geometric_meaning': '反映流形上的局部几何结构'
        }

        # 分析几何结构 G(z) 的特性
        geometric_analysis = {
            'weight_type': self.graph_structure.weight_mode,
            'sparsity': structure_info['implementation']['graph_sparsity'],
            'weight_distribution': structure_info['implementation']['weight_statistics'],
            'graph_properties': self._compute_graph_properties(weights),
            'geometric_meaning': '编码不同的几何关系（权重图、测地距离图、扩散核等）'
        }

        # 验证统一抽象的有效性
        abstraction_validation = {
            'theoretical_form': 'G(z) := Graph(z, N(z), w_ij)',
            'component_verification': {
                'z_verified': Z.shape,
                'N_z_verified': neighbors.shape,
                'w_ij_verified': weights.shape
            },
            'common_patterns': {
                'local_connectivity': np.mean(np.sum(weights > 0, axis=1)) / self.graph_structure.k_neighbors,
                'weight_normalization': np.allclose(np.sum(weights, axis=1), 1.0),
                'sparsity_pattern': structure_info['implementation']['graph_sparsity'] < 0.1
            }
        }

        return {
            'neighbor_structure_N_z': neighbor_analysis,
            'geometric_structure_G_z': geometric_analysis,
            'abstraction_validation': abstraction_validation,
            'structure_info': structure_info
        }

    def _compute_neighbor_consistency(self, neighbors: np.ndarray) -> float:
        """计算邻居一致性"""
        n_samples = neighbors.shape[0]
        consistency_scores = []

        for i in range(min(n_samples, 50)):  # 采样计算
            for j in range(i+1, min(n_samples, 50)):
                overlap = len(set(neighbors[i]) & set(neighbors[j]))
                max_possible = min(len(neighbors[i]), len(neighbors[j]))
                if max_possible > 0:
                    consistency_scores.append(overlap / max_possible)

        return np.mean(consistency_scores) if consistency_scores else 0.0

    def _compute_local_density(self, Z: np.ndarray, neighbors: np.ndarray) -> np.ndarray:
        """计算局部密度"""
        densities = []
        for i in range(Z.shape[0]):
            neighbor_coords = Z[neighbors[i]]
            center = Z[i]
            distances = np.linalg.norm(neighbor_coords - center, axis=1)
            density = 1.0 / (np.mean(distances) + 1e-8)
            densities.append(density)
        return np.array(densities)

    def _compute_graph_properties(self, weights: np.ndarray) -> Dict:
        """计算图的拓扑特性"""
        # 度分布
        degrees = np.sum(weights > 0, axis=1)

        # 权重统计
        nonzero_weights = weights[weights > 0]

        return {
            'avg_degree': np.mean(degrees),
            'degree_std': np.std(degrees),
            'weight_entropy': -np.sum(nonzero_weights * np.log(nonzero_weights + 1e-10)),
            'clustering_approximation': np.mean(degrees) / weights.shape[0]
        }

    def visualize_manifold_graph_structure(self, Z: np.ndarray, method_name: str = None,
                                          save_path: str = None) -> Dict:
        """
        可视化流形图结构

        参数:
            Z: 低维嵌入数据
            method_name: 方法名称
            save_path: 保存路径

        返回:
            visualization_info: 可视化信息
        """
        if method_name is None:
            method_name = f"{self.graph_structure.weight_mode.upper()} Manifold Graph"

        # 使用图结构进行可视化
        viz_info = self.graph_structure.visualize_manifold_graph(Z, method_name, save_path)

        return viz_info


# ================================ 可视化和数据集部分 ================================

# 可视化相关导入
import matplotlib.pyplot as plt
import networkx as nx
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
from matplotlib.patches import FancyBboxPatch
import matplotlib.patches as mpatches

# 中文字体和高质量图片设置
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['figure.dpi'] = 150
matplotlib.rcParams['savefig.dpi'] = 300
matplotlib.rcParams['savefig.bbox'] = 'tight'
matplotlib.rcParams['savefig.pad_inches'] = 0.1

class ManifoldGraphVisualizer:
    """
    流形图结构专业可视化器

    使用NetworkX和其他专业工具绘制流形图结构 G(z) := Graph(z, N(z), w_ij)
    支持中文显示和高质量图片输出
    """

    def __init__(self, figsize: Tuple[int, int] = (18, 12), dpi: int = 300):
        self.figsize = figsize
        self.dpi = dpi
        self._setup_chinese_fonts()

    def _setup_chinese_fonts(self):
        """设置中文字体和高质量显示"""
        # 设置非交互式后端
        import matplotlib
        import matplotlib.pyplot as plt
        matplotlib.use('Agg')  # 非交互式后端

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 设置高质量显示
        plt.rcParams['figure.dpi'] = 150
        plt.rcParams['savefig.dpi'] = self.dpi
        plt.rcParams['savefig.bbox'] = 'tight'
        plt.rcParams['savefig.pad_inches'] = 0.2
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['xtick.labelsize'] = 9
        plt.rcParams['ytick.labelsize'] = 9
        plt.rcParams['legend.fontsize'] = 9

    def visualize_graph_structure(self, Z: np.ndarray, neighbors: np.ndarray,
                                weights: np.ndarray, method_name: str = "Manifold",
                                save_path: str = None) -> Dict:
        """
        可视化流形图结构

        参数:
            Z: 低维嵌入数据 (n_samples, d)
            neighbors: 邻居索引矩阵 (n_samples, k_neighbors)
            weights: 权重矩阵 (n_samples, k_neighbors)
            method_name: 方法名称
            save_path: 保存路径

        返回:
            visualization_info: 可视化信息
        """
 
        # 创建NetworkX图
        G = self._create_networkx_graph(Z, neighbors, weights)

        # 创建高质量多子图布局
        fig, axes = plt.subplots(2, 3, figsize=self.figsize, dpi=150)
        fig.suptitle(f'{method_name} 流形图结构可视化\nG(z) := Graph(z, N(z), w_ij)',
                    fontsize=18, fontweight='bold', y=0.98)

        # 1. 原始嵌入空间散点图
        ax1 = axes[0, 0]
        scatter = ax1.scatter(Z[:, 0], Z[:, 1], c=range(len(Z)), cmap='viridis',
                             s=60, alpha=0.8, edgecolors='white', linewidth=0.5)
        ax1.set_title('嵌入空间 z ∈ R^d', fontsize=12, fontweight='bold')
        ax1.set_xlabel('z₁', fontsize=11)
        ax1.set_ylabel('z₂', fontsize=11)
        ax1.grid(True, alpha=0.3)
        cbar1 = plt.colorbar(scatter, ax=ax1, shrink=0.8)
        cbar1.set_label('节点索引', fontsize=10)

        # 2. 网络图 - Spring布局
        ax2 = axes[0, 1]
        pos = nx.spring_layout(G, k=1.5, iterations=100, seed=42)
        self._draw_network(G, pos, ax2, "Spring布局 - 邻居集合 N(z)")

        # 3. 网络图 - 基于嵌入坐标的布局
        ax3 = axes[0, 2]
        pos_embed = {i: Z[i] for i in range(len(Z))}
        self._draw_network(G, pos_embed, ax3, "嵌入坐标布局 - 几何结构 G(z)")

        # 4. 权重分布直方图
        ax4 = axes[1, 0]
        weights_flat = weights.flatten()
        weights_nonzero = weights_flat[weights_flat > 0]
        n, bins, patches = ax4.hist(weights_nonzero, bins=30, alpha=0.8,
                                   color='skyblue', edgecolor='navy', linewidth=0.8)
        ax4.set_xlabel('权重值 w_ij', fontsize=11)
        ax4.set_ylabel('频次', fontsize=11)
        ax4.set_title('权重分布', fontsize=12, fontweight='bold')
        ax4.set_yscale('log')
        ax4.grid(True, alpha=0.3)

        # 添加统计信息
        mean_weight = np.mean(weights_nonzero)
        ax4.axvline(mean_weight, color='red', linestyle='--', linewidth=2,
                   label=f'均值: {mean_weight:.4f}')
        ax4.legend(fontsize=9)

        # 5. 度分布
        ax5 = axes[1, 1]
        degrees = [G.degree(n) for n in G.nodes()]
        n, bins, patches = ax5.hist(degrees, bins=20, alpha=0.8,
                                   color='lightcoral', edgecolor='darkred', linewidth=0.8)
        ax5.set_xlabel('节点度数', fontsize=11)
        ax5.set_ylabel('频次', fontsize=11)
        ax5.set_title('度分布', fontsize=12, fontweight='bold')
        ax5.grid(True, alpha=0.3)

        # 添加平均度
        avg_degree = np.mean(degrees)
        ax5.axvline(avg_degree, color='blue', linestyle='--', linewidth=2,
                   label=f'平均度: {avg_degree:.2f}')
        ax5.legend(fontsize=9)

        # 6. 权重热图（子集）
        ax6 = axes[1, 2]
        n_show = min(50, len(Z))
        weight_matrix_full = np.zeros((len(Z), len(Z)))
        for i in range(len(Z)):
            weight_matrix_full[i, neighbors[i]] = weights[i]

        im = ax6.imshow(weight_matrix_full[:n_show, :n_show], cmap='viridis',
                       aspect='auto', interpolation='nearest')
        ax6.set_title(f'权重矩阵 w_ij (前{n_show}×{n_show})', fontsize=12, fontweight='bold')
        ax6.set_xlabel('节点索引', fontsize=11)
        ax6.set_ylabel('节点索引', fontsize=11)
        cbar6 = plt.colorbar(im, ax=ax6, shrink=0.8)
        cbar6.set_label('权重值', fontsize=10)

        # 调整布局
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])

        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"高质量图结构可视化保存到: {save_path}")

        # 计算图统计信息
        graph_stats = self._compute_graph_statistics(G, weights)

        return {
            'graph': G,
            'positions': pos_embed,
            'statistics': graph_stats,
            'visualization_saved': save_path is not None
        }

    def _create_networkx_graph(self, Z: np.ndarray, neighbors: np.ndarray,
                              weights: np.ndarray) -> 'nx.Graph':
        """创建NetworkX图对象"""
        import networkx as nx

        G = nx.Graph()

        # 添加节点
        for i in range(len(Z)):
            G.add_node(i, pos=Z[i], embedding=Z[i])

        # 添加边
        for i in range(len(Z)):
            for j, neighbor_idx in enumerate(neighbors[i]):
                weight = weights[i, j]
                if weight > 0:
                    G.add_edge(i, neighbor_idx, weight=weight)

        return G

    def _draw_network(self, G: 'nx.Graph', pos: Dict, ax: plt.Axes, title: str):
        """绘制高质量网络图"""
        
        import networkx as nx
  

        # 获取边权重用于可视化
        edges = G.edges()
        if len(edges) == 0:
            ax.text(0.5, 0.5, '无边连接', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(title, fontsize=12, fontweight='bold')
            return

        weights = [G[u][v]['weight'] for u, v in edges]

        # 归一化权重用于边宽度和透明度
        if weights:
            max_weight = max(weights)
            min_weight = min(weights)
            if max_weight > min_weight:
                # 边宽度：0.5-3.0
                edge_widths = [0.5 + 2.5 * (w - min_weight) / (max_weight - min_weight) for w in weights]
                # 边透明度：0.3-0.8
                edge_alphas = [0.3 + 0.5 * (w - min_weight) / (max_weight - min_weight) for w in weights]
            else:
                edge_widths = [1.5] * len(weights)
                edge_alphas = [0.6] * len(weights)
        else:
            edge_widths = []
            edge_alphas = []

        # 绘制边 - 使用渐变颜色
        edge_colors = plt.cm.Blues([0.3 + 0.7 * (w - min(weights)) / (max(weights) - min(weights))
                                   for w in weights]) if weights else []

        nx.draw_networkx_edges(G, pos, ax=ax, width=edge_widths, alpha=0.7,
                              edge_color=edge_colors, edge_cmap=plt.cm.Blues)

        # 绘制节点 - 更大更清晰
        node_colors = list(range(len(G.nodes())))
        nodes = nx.draw_networkx_nodes(G, pos, ax=ax, node_color=node_colors,
                                      node_size=80, cmap='viridis', alpha=0.9,
                                      edgecolors='white', linewidths=1)

        # 设置标题和样式
        ax.set_title(title, fontsize=12, fontweight='bold', pad=10)
        ax.axis('off')

        # 添加图例（如果节点数不太多）
        if len(G.nodes()) <= 20:
            # 添加少量节点标签
            labels = {i: str(i) for i in list(G.nodes())[:10]}
            nx.draw_networkx_labels(G, pos, labels, ax=ax, font_size=8,
                                   font_color='white', font_weight='bold')

    def _compute_graph_statistics(self, G: 'nx.Graph', weights: np.ndarray) -> Dict:
        """计算图统计信息"""
        import networkx as nx

        stats = {
            'num_nodes': G.number_of_nodes(),
            'num_edges': G.number_of_edges(),
            'density': nx.density(G),
            'is_connected': nx.is_connected(G),
            'num_components': nx.number_connected_components(G),
            'avg_clustering': nx.average_clustering(G),
            'avg_degree': np.mean([d for n, d in G.degree()]),
            'weight_statistics': {
                'mean': np.mean(weights[weights > 0]),
                'std': np.std(weights[weights > 0]),
                'min': np.min(weights[weights > 0]),
                'max': np.max(weights[weights > 0])
            }
        }

        if nx.is_connected(G):
            stats['avg_shortest_path'] = nx.average_shortest_path_length(G)
            stats['diameter'] = nx.diameter(G)

        return stats

    def _basic_visualization(self, Z: np.ndarray, neighbors: np.ndarray,
                           weights: np.ndarray, method_name: str, save_path: str) -> Dict:
        """高质量基础可视化（不依赖NetworkX）"""
        fig, axes = plt.subplots(2, 2, figsize=self.figsize, dpi=150)
        fig.suptitle(f'{method_name} 流形图结构 (基础可视化)\nG(z) := Graph(z, N(z), w_ij)',
                    fontsize=16, fontweight='bold', y=0.98)

        # 1. 嵌入空间
        ax1 = axes[0, 0]
        scatter = ax1.scatter(Z[:, 0], Z[:, 1], c=range(len(Z)), cmap='viridis',
                             s=60, alpha=0.8, edgecolors='white', linewidth=0.5)
        ax1.set_title('嵌入空间 z ∈ R^d', fontsize=12, fontweight='bold')
        ax1.set_xlabel('z₁', fontsize=11)
        ax1.set_ylabel('z₂', fontsize=11)
        ax1.grid(True, alpha=0.3)
        cbar1 = plt.colorbar(scatter, ax=ax1, shrink=0.8)
        cbar1.set_label('节点索引', fontsize=10)

        # 2. 连接图
        ax2 = axes[0, 1]
        scatter2 = ax2.scatter(Z[:, 0], Z[:, 1], c=range(len(Z)), cmap='viridis',
                              s=60, alpha=0.8, edgecolors='white', linewidth=0.5)

        # 绘制连接 - 只显示强连接，使用权重编码线宽
        threshold = np.percentile(weights[weights > 0], 75)  # 前25%的强连接
        max_weight = np.max(weights)
        min_weight = np.min(weights[weights > 0])

        for i in range(len(Z)):
            for j, neighbor_idx in enumerate(neighbors[i]):
                if weights[i, j] >= threshold:
                    # 根据权重设置线宽和透明度
                    normalized_weight = (weights[i, j] - min_weight) / (max_weight - min_weight)
                    linewidth = 0.5 + 2.0 * normalized_weight
                    alpha = 0.3 + 0.5 * normalized_weight

                    ax2.plot([Z[i, 0], Z[neighbor_idx, 0]],
                            [Z[i, 1], Z[neighbor_idx, 1]],
                            'navy', alpha=alpha, linewidth=linewidth)

        ax2.set_title('邻居连接 N(z) (强连接)', fontsize=12, fontweight='bold')
        ax2.set_xlabel('z₁', fontsize=11)
        ax2.set_ylabel('z₂', fontsize=11)
        ax2.grid(True, alpha=0.3)

        # 3. 权重分布
        ax3 = axes[1, 0]
        weights_nonzero = weights[weights > 0]
        n, bins, patches = ax3.hist(weights_nonzero, bins=30, alpha=0.8,
                                   color='skyblue', edgecolor='navy', linewidth=0.8)
        ax3.set_title('权重分布 w_ij', fontsize=12, fontweight='bold')
        ax3.set_xlabel('权重值', fontsize=11)
        ax3.set_ylabel('频次', fontsize=11)
        ax3.set_yscale('log')
        ax3.grid(True, alpha=0.3)

        # 添加统计信息
        mean_weight = np.mean(weights_nonzero)
        ax3.axvline(mean_weight, color='red', linestyle='--', linewidth=2,
                   label=f'均值: {mean_weight:.4f}')
        ax3.legend(fontsize=9)

        # 4. 度分布
        ax4 = axes[1, 1]
        degrees = np.sum(weights > 0, axis=1)
        n, bins, patches = ax4.hist(degrees, bins=20, alpha=0.8,
                                   color='lightcoral', edgecolor='darkred', linewidth=0.8)
        ax4.set_title('度分布', fontsize=12, fontweight='bold')
        ax4.set_xlabel('节点度数', fontsize=11)
        ax4.set_ylabel('频次', fontsize=11)
        ax4.grid(True, alpha=0.3)

        # 添加平均度
        avg_degree = np.mean(degrees)
        ax4.axvline(avg_degree, color='blue', linestyle='--', linewidth=2,
                   label=f'平均度: {avg_degree:.2f}')
        ax4.legend(fontsize=9)

        plt.tight_layout(rect=[0, 0.03, 1, 0.95])

        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"高质量基础可视化保存到: {save_path}")

        return {'basic_visualization': True, 'high_quality': True}


def generate_swiss_roll_data(n_samples: int = 1000, noise: float = 0.1) -> Tuple[np.ndarray, np.ndarray]:
    """生成Swiss Roll数据集"""
    t = 1.5 * np.pi * (1 + 2 * np.random.rand(n_samples))
    height = 21 * np.random.rand(n_samples)

    X = np.zeros((n_samples, 3))
    X[:, 0] = t * np.cos(t)
    X[:, 1] = height
    X[:, 2] = t * np.sin(t)
    X += noise * np.random.randn(n_samples, 3)

    return X, t

# ================================ 演示例子 ================================

def demonstrate_universal_decoder():
    """
    通用流形解码器演示例子

    展示完整流程：数据生成 → 降维 → 网络图构建 → 解码器训练 → 性能评估 → 可视化
    """
    print("通用流形解码器演示")
    print("=" * 50)

    # 第1步：生成测试数据
    print("\n第1步：生成Swiss Roll数据")
    X, t = generate_swiss_roll_data(n_samples=200, noise=0.1)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    print(f"原始数据形状: {X.shape}")

    # 第2步：降维
    print("\n第2步：LLE降维")
    lle = LocallyLinearEmbedding(n_components=2, n_neighbors=10, random_state=42)
    Z = lle.fit_transform(X_scaled)
    print(f"嵌入数据形状: {Z.shape}")

    # 第3步：构建流形图结构
    print("\n第3步：构建流形图结构 G(z)")
    graph_structure = ManifoldGraphStructure(k_neighbors=8, weight_mode='gaussian')
    neighbors, weights = graph_structure.build_graph(Z)
    print(f"邻居矩阵形状: {neighbors.shape}")
    print(f"权重矩阵形状: {weights.shape}")
    print(f"平均权重: {np.mean(weights[weights > 0]):.4f}")

    # 第4步：训练解码器
    print("\n第4步：训练神经网络解码器")
    decoder_system = UniversalManifoldSystem(
        embedding_dim=2, original_dim=3,
        k_neighbors=8, weight_mode='gaussian'
    )
    history = decoder_system.fit(Z, X_scaled, epochs=100, verbose=True)

    # 第5步：性能评估
    print("\n第5步：性能评估")
    metrics = decoder_system.evaluate(Z, X_scaled)
    print(f"相关系数: {metrics['correlation']:.4f}")
    print(f"RMSE: {metrics['rmse']:.6f}")
    print(f"Frobenius误差: {metrics['frobenius_error']:.6f}")

    # 第6步：可视化结果
    print("\n第6步：可视化结果")
    X_pred = decoder_system.decode(Z)

    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('通用流形解码器演示结果', fontsize=16, fontweight='bold')

    # 原始3D数据
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    ax1.scatter(X[:, 0], X[:, 1], X[:, 2], c=t, cmap='viridis', s=20)
    ax1.set_title('原始Swiss Roll数据')
    ax1.set_xlabel('X1')
    ax1.set_ylabel('X2')
    ax1.set_zlabel('X3')

    # 2D嵌入
    ax2 = axes[0, 1]
    scatter = ax2.scatter(Z[:, 0], Z[:, 1], c=t, cmap='viridis', s=20)
    ax2.set_title('LLE嵌入')
    ax2.set_xlabel('Z1')
    ax2.set_ylabel('Z2')
    ax2.grid(True, alpha=0.3)

    # 网络图 - 增强版可视化
    ax3 = axes[0, 2]
    # 绘制所有节点
    scatter3 = ax3.scatter(Z[:, 0], Z[:, 1], c=t, cmap='viridis', s=50, alpha=0.8,
                          edgecolors='white', linewidth=1, zorder=3)

    # 绘制所有邻居连接 - 根据权重分层显示
    max_weight = np.max(weights)
    min_weight = np.min(weights[weights > 0])

    # 强连接（权重前25%）
    strong_threshold = np.percentile(weights[weights > 0], 75)
    # 中等连接（权重25%-75%）
    medium_threshold = np.percentile(weights[weights > 0], 25)

    for i in range(len(Z)):
        for j, neighbor_idx in enumerate(neighbors[i]):
            if weights[i, j] > 0:
                # 根据权重设置不同的线条样式
                normalized_weight = (weights[i, j] - min_weight) / (max_weight - min_weight)

                if weights[i, j] >= strong_threshold:
                    # 强连接：粗线，高透明度
                    linewidth = 1.5 + 1.0 * normalized_weight
                    alpha = 0.6 + 0.3 * normalized_weight
                    color = 'darkblue'
                elif weights[i, j] >= medium_threshold:
                    # 中等连接：中等线条
                    linewidth = 0.8 + 0.7 * normalized_weight
                    alpha = 0.3 + 0.3 * normalized_weight
                    color = 'navy'
                else:
                    # 弱连接：细线，低透明度
                    linewidth = 0.3 + 0.4 * normalized_weight
                    alpha = 0.1 + 0.2 * normalized_weight
                    color = 'lightblue'

                ax3.plot([Z[i, 0], Z[neighbor_idx, 0]],
                        [Z[i, 1], Z[neighbor_idx, 1]],
                        color=color, alpha=alpha, linewidth=linewidth, zorder=1)

    ax3.set_title('流形图结构 G(z)\n(权重编码网络)', fontweight='bold')
    ax3.set_xlabel('Z1')
    ax3.set_ylabel('Z2')
    ax3.grid(True, alpha=0.3)

    # 添加图例说明
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='darkblue', lw=2, alpha=0.8, label='强连接 (>75%)'),
        Line2D([0], [0], color='navy', lw=1.5, alpha=0.6, label='中等连接 (25%-75%)'),
        Line2D([0], [0], color='lightblue', lw=1, alpha=0.4, label='弱连接 (<25%)')
    ]
    ax3.legend(handles=legend_elements, loc='upper right', fontsize=8)

    # 重构3D数据
    ax4 = fig.add_subplot(2, 3, 4, projection='3d')
    ax4.scatter(X_pred[:, 0], X_pred[:, 1], X_pred[:, 2], c=t, cmap='viridis', s=20)
    ax4.set_title('解码器重构数据')
    ax4.set_xlabel('X1')
    ax4.set_ylabel('X2')
    ax4.set_zlabel('X3')

    # 训练损失
    ax5 = axes[1, 1]
    ax5.plot(history['train_loss'], 'b-', label='训练损失')
    if 'val_loss' in history:
        ax5.plot(history['val_loss'], 'r-', label='验证损失')
    ax5.set_title('训练过程')
    ax5.set_xlabel('轮次')
    ax5.set_ylabel('损失')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # 重构误差对比
    ax6 = axes[1, 2]
    errors = np.abs(X_scaled - X_pred)
    ax6.hist(errors.flatten(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax6.set_title('重构误差分布')
    ax6.set_xlabel('绝对误差')
    ax6.set_ylabel('频次')
    ax6.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('universal_decoder_demo.png', dpi=300, bbox_inches='tight')
    print("✓ 结果保存到: universal_decoder_demo.png")

    return {
        'original_data': X,
        'embedding': Z,
        'reconstructed': X_pred,
        'metrics': metrics,
        'history': history
    }


def demonstrate_network_visualization():
    """
    专门的网络图可视化演示

    详细展示流形图结构 G(z) := Graph(z, N(z), w_ij) 的网络可视化
    """
    print("\n网络图可视化详细演示")
    print("=" * 40)

    # 生成数据
    X, t = generate_swiss_roll_data(n_samples=80, noise=0.05)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # LLE降维
    lle = LocallyLinearEmbedding(n_components=2, n_neighbors=6, random_state=42)
    Z = lle.fit_transform(X_scaled)

    print(f"数据形状: {X.shape} -> {Z.shape}")

    # 测试不同权重模式的网络图
    weight_modes = ['euclidean', 'gaussian', 'lle']
    mode_names = ['欧氏距离权重', '高斯核权重', 'LLE局部线性权重']

    # 创建大型可视化 - 增加spring布局对比
    fig, axes = plt.subplots(3, 3, figsize=(18, 18))
    fig.suptitle('流形图结构网络可视化详细对比\nG(z) := Graph(z, N(z), w_ij)',
                fontsize=16, fontweight='bold')

    for i, (mode, name) in enumerate(zip(weight_modes, mode_names)):
        print(f"\n{i+1}. 构建 {name} 网络图...")

        # 构建图结构
        graph_structure = ManifoldGraphStructure(k_neighbors=6, weight_mode=mode)
        neighbors, weights = graph_structure.build_graph(Z)

        # 第1排：仅节点图
        ax_nodes = axes[0, i]
        scatter = ax_nodes.scatter(Z[:, 0], Z[:, 1], c=t, cmap='viridis',
                                 s=60, alpha=0.8, edgecolors='black', linewidth=0.8)
        ax_nodes.set_title(f'{name}\n(仅节点)', fontweight='bold')
        ax_nodes.set_xlabel('z₁')
        ax_nodes.set_ylabel('z₂')
        ax_nodes.grid(True, alpha=0.3)

        # 第2排：嵌入坐标网络图
        ax_network = axes[1, i]
        ax_network.scatter(Z[:, 0], Z[:, 1], c=t, cmap='viridis',
                         s=60, alpha=0.8, edgecolors='white', linewidth=1, zorder=3)

        # 绘制所有连接 - 权重编码
        max_weight = np.max(weights)
        min_weight = np.min(weights[weights > 0])

        # 分层显示不同强度的连接
        strong_threshold = np.percentile(weights[weights > 0], 75)
        medium_threshold = np.percentile(weights[weights > 0], 50)
        weak_threshold = np.percentile(weights[weights > 0], 25)

        for j in range(len(Z)):
            for k, neighbor_idx in enumerate(neighbors[j]):
                if weights[j, k] > 0:
                    normalized_weight = (weights[j, k] - min_weight) / (max_weight - min_weight)

                    if weights[j, k] >= strong_threshold:
                        # 强连接
                        linewidth = 1.5 + 1.0 * normalized_weight
                        alpha = 0.7 + 0.2 * normalized_weight
                        color = 'darkred'
                    elif weights[j, k] >= medium_threshold:
                        # 中等连接
                        linewidth = 1.0 + 0.8 * normalized_weight
                        alpha = 0.5 + 0.3 * normalized_weight
                        color = 'darkblue'
                    elif weights[j, k] >= weak_threshold:
                        # 较弱连接
                        linewidth = 0.6 + 0.6 * normalized_weight
                        alpha = 0.3 + 0.3 * normalized_weight
                        color = 'navy'
                    else:
                        # 最弱连接
                        linewidth = 0.3 + 0.4 * normalized_weight
                        alpha = 0.1 + 0.2 * normalized_weight
                        color = 'lightblue'

                    ax_network.plot([Z[j, 0], Z[neighbor_idx, 0]],
                                  [Z[j, 1], Z[neighbor_idx, 1]],
                                  color=color, alpha=alpha, linewidth=linewidth, zorder=1)

        ax_network.set_title(f'{name}\n(嵌入坐标网络)', fontweight='bold')
        ax_network.set_xlabel('z₁')
        ax_network.set_ylabel('z₂')
        ax_network.grid(True, alpha=0.3)

        # 第3排：Spring布局网络图（简化版）
        ax_spring = axes[2, i]

        # 简化的spring布局 - 不依赖NetworkX
        # 使用力导向算法的简单实现
        n_nodes = len(Z)

        # 初始化spring位置（基于嵌入坐标的扰动）
        spring_pos = Z.copy()
        spring_pos += 0.1 * np.random.randn(*spring_pos.shape)

        # 简单的力导向迭代
        for iteration in range(20):
            forces = np.zeros_like(spring_pos)

            # 计算排斥力（所有节点对之间）
            for i_node in range(n_nodes):
                for j_node in range(n_nodes):
                    if i_node != j_node:
                        diff = spring_pos[i_node] - spring_pos[j_node]
                        dist = np.linalg.norm(diff) + 1e-6
                        # 排斥力
                        forces[i_node] += 0.1 * diff / (dist**2)

            # 计算吸引力（邻居之间）
            for i_node in range(n_nodes):
                for j, neighbor_idx in enumerate(neighbors[i_node]):
                    if weights[i_node, j] > np.percentile(weights[weights > 0], 50):
                        diff = spring_pos[neighbor_idx] - spring_pos[i_node]
                        dist = np.linalg.norm(diff) + 1e-6
                        # 吸引力，权重编码
                        force_strength = weights[i_node, j] * 0.05
                        forces[i_node] += force_strength * diff / dist

            # 更新位置
            spring_pos += 0.1 * forces

        # 绘制spring布局网络
        ax_spring.scatter(spring_pos[:, 0], spring_pos[:, 1], c=t, cmap='viridis',
                         s=60, alpha=0.8, edgecolors='white', linewidth=1, zorder=3)

        # 绘制连接
        strong_threshold = np.percentile(weights[weights > 0], 60)
        for j in range(len(Z)):
            for k, neighbor_idx in enumerate(neighbors[j]):
                if weights[j, k] >= strong_threshold:
                    normalized_weight = (weights[j, k] - min_weight) / (max_weight - min_weight)
                    linewidth = 0.5 + 1.5 * normalized_weight
                    alpha = 0.3 + 0.5 * normalized_weight

                    if weights[j, k] >= np.percentile(weights[weights > 0], 75):
                        color = 'darkred'
                    elif weights[j, k] >= np.percentile(weights[weights > 0], 50):
                        color = 'darkblue'
                    else:
                        color = 'lightblue'

                    ax_spring.plot([spring_pos[j, 0], spring_pos[neighbor_idx, 0]],
                                  [spring_pos[j, 1], spring_pos[neighbor_idx, 1]],
                                  color=color, alpha=alpha, linewidth=linewidth, zorder=1)

        ax_spring.set_title(f'{name}\n(Spring布局网络)', fontweight='bold')
        ax_spring.set_xlabel('Spring X')
        ax_spring.set_ylabel('Spring Y')
        ax_spring.grid(True, alpha=0.3)

        # 输出统计信息
        n_edges = np.sum(weights > 0)
        avg_weight = np.mean(weights[weights > 0])
        print(f"   节点数: {len(Z)}")
        print(f"   边数: {n_edges}")
        print(f"   平均权重: {avg_weight:.4f}")
        print(f"   权重范围: [{np.min(weights[weights > 0]):.4f}, {np.max(weights):.4f}]")

    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=axes, shrink=0.4, pad=0.02)
    cbar.set_label('Swiss Roll 参数 t', fontsize=12)

    # 添加网络连接图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='darkred', lw=2, alpha=0.8, label='强连接 (>75%)'),
        Line2D([0], [0], color='darkblue', lw=1.5, alpha=0.7, label='中等连接 (50%-75%)'),
        Line2D([0], [0], color='navy', lw=1, alpha=0.5, label='较弱连接 (25%-50%)'),
        Line2D([0], [0], color='lightblue', lw=0.7, alpha=0.3, label='最弱连接 (<25%)')
    ]
    fig.legend(handles=legend_elements, loc='lower center', ncol=4,
              bbox_to_anchor=(0.5, -0.05), fontsize=10)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)
    plt.savefig('detailed_network_visualization.png', dpi=300, bbox_inches='tight')
    print(f"\n✓ 详细网络可视化保存到: detailed_network_visualization.png")
    print("  包含3种布局对比:")
    print("    - 第1排: 仅节点图")
    print("    - 第2排: 嵌入坐标网络图")
    print("    - 第3排: Spring布局网络图")

    return True

def demonstrate_weight_modes():
    """
    演示不同权重模式的效果
    """
    print("\n权重模式对比演示")
    print("=" * 30)

    # 生成数据
    X, t = generate_swiss_roll_data(n_samples=100, noise=0.05)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # LLE降维
    lle = LocallyLinearEmbedding(n_components=2, n_neighbors=8, random_state=42)
    Z = lle.fit_transform(X_scaled)

    # 测试不同权重模式
    weight_modes = ['euclidean', 'gaussian', 'lle']

    print("\n权重模式对比:")
    for mode in weight_modes:
        decoder = UniversalManifoldSystem(
            embedding_dim=2, original_dim=3,
            k_neighbors=8, weight_mode=mode
        )
        decoder.fit(Z, X_scaled, epochs=50, verbose=False)
        metrics = decoder.evaluate(Z, X_scaled)
        print(f"{mode:>10}: 相关系数 = {metrics['correlation']:.4f}")

    return True

def demonstrate_graph_analysis():
    """
    图结构分析可视化演示

    展示图的统计特性和网络分析
    """
    print("\n图结构分析演示")
    print("=" * 30)

    # 生成数据
    X, t = generate_swiss_roll_data(n_samples=60, noise=0.05)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # LLE降维
    lle = LocallyLinearEmbedding(n_components=2, n_neighbors=8, random_state=42)
    Z = lle.fit_transform(X_scaled)

    # 构建图结构
    graph_structure = ManifoldGraphStructure(k_neighbors=8, weight_mode='gaussian')
    neighbors, weights = graph_structure.build_graph(Z)

    # 创建分析可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('流形图结构分析\nG(z) := Graph(z, N(z), w_ij)',
                fontsize=16, fontweight='bold')

    # 1. 网络图 - 节点度数可视化
    ax1 = axes[0, 0]
    # 计算每个节点的度数（连接数）
    node_degrees = np.sum(weights > 0, axis=1)
    scatter1 = ax1.scatter(Z[:, 0], Z[:, 1], c=node_degrees, cmap='Reds',
                          s=80, alpha=0.8, edgecolors='black', linewidth=0.8)

    # 绘制连接
    for i in range(len(Z)):
        for j, neighbor_idx in enumerate(neighbors[i]):
            if weights[i, j] > np.percentile(weights[weights > 0], 60):
                ax1.plot([Z[i, 0], Z[neighbor_idx, 0]],
                        [Z[i, 1], Z[neighbor_idx, 1]],
                        'gray', alpha=0.3, linewidth=0.5)

    ax1.set_title('节点度数分布', fontweight='bold')
    ax1.set_xlabel('z₁')
    ax1.set_ylabel('z₂')
    ax1.grid(True, alpha=0.3)
    cbar1 = plt.colorbar(scatter1, ax=ax1, shrink=0.8)
    cbar1.set_label('节点度数')

    # 2. 权重分布直方图
    ax2 = axes[0, 1]
    weights_flat = weights[weights > 0]
    ax2.hist(weights_flat, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.set_title('权重分布直方图', fontweight='bold')
    ax2.set_xlabel('权重值')
    ax2.set_ylabel('频次')
    ax2.grid(True, alpha=0.3)

    # 添加统计信息
    ax2.axvline(np.mean(weights_flat), color='red', linestyle='--',
               label=f'均值: {np.mean(weights_flat):.4f}')
    ax2.axvline(np.median(weights_flat), color='green', linestyle='--',
               label=f'中位数: {np.median(weights_flat):.4f}')
    ax2.legend()

    # 3. 距离vs权重散点图
    ax3 = axes[1, 0]
    # 计算欧氏距离
    distances = []
    weights_for_dist = []
    for i in range(len(Z)):
        for j, neighbor_idx in enumerate(neighbors[i]):
            if weights[i, j] > 0:
                dist = np.linalg.norm(Z[i] - Z[neighbor_idx])
                distances.append(dist)
                weights_for_dist.append(weights[i, j])

    ax3.scatter(distances, weights_for_dist, alpha=0.6, s=30, color='purple')
    ax3.set_title('距离 vs 权重关系', fontweight='bold')
    ax3.set_xlabel('欧氏距离')
    ax3.set_ylabel('权重值')
    ax3.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(distances, weights_for_dist, 1)
    p = np.poly1d(z)
    ax3.plot(sorted(distances), p(sorted(distances)), "r--", alpha=0.8,
            label=f'趋势线 (斜率: {z[0]:.4f})')
    ax3.legend()

    # 4. 邻居连接模式
    ax4 = axes[1, 1]
    # 显示特定节点的邻居连接
    center_node = len(Z) // 2  # 选择中心附近的节点

    # 绘制所有节点
    ax4.scatter(Z[:, 0], Z[:, 1], c='lightgray', s=40, alpha=0.6)

    # 高亮中心节点
    ax4.scatter(Z[center_node, 0], Z[center_node, 1], c='red', s=120,
               marker='*', edgecolors='black', linewidth=2, label='中心节点')

    # 高亮邻居节点
    neighbor_indices = neighbors[center_node]
    ax4.scatter(Z[neighbor_indices, 0], Z[neighbor_indices, 1],
               c='orange', s=80, alpha=0.8, edgecolors='black', linewidth=1,
               label='邻居节点')

    # 绘制连接
    for j, neighbor_idx in enumerate(neighbor_indices):
        weight = weights[center_node, j]
        linewidth = 1 + 3 * (weight / np.max(weights[center_node]))
        ax4.plot([Z[center_node, 0], Z[neighbor_idx, 0]],
                [Z[center_node, 1], Z[neighbor_idx, 1]],
                'darkred', alpha=0.7, linewidth=linewidth)

        # 添加权重标签
        mid_x = (Z[center_node, 0] + Z[neighbor_idx, 0]) / 2
        mid_y = (Z[center_node, 1] + Z[neighbor_idx, 1]) / 2
        ax4.text(mid_x, mid_y, f'{weight:.3f}', fontsize=8,
                bbox=dict(boxstyle="round,pad=0.1", facecolor="white", alpha=0.8))

    ax4.set_title(f'节点 {center_node} 的邻居连接', fontweight='bold')
    ax4.set_xlabel('z₁')
    ax4.set_ylabel('z₂')
    ax4.grid(True, alpha=0.3)
    ax4.legend()

    plt.tight_layout()
    plt.savefig('graph_structure_analysis.png', dpi=300, bbox_inches='tight')
    print(f"✓ 图结构分析保存到: graph_structure_analysis.png")

    # 输出详细统计
    print(f"\n图结构统计信息:")
    print(f"  节点数: {len(Z)}")
    print(f"  总边数: {np.sum(weights > 0)}")
    print(f"  平均度数: {np.mean(node_degrees):.2f}")
    print(f"  度数范围: [{np.min(node_degrees)}, {np.max(node_degrees)}]")
    print(f"  权重统计: 均值={np.mean(weights_flat):.4f}, 标准差={np.std(weights_flat):.4f}")
    print(f"  图密度: {np.sum(weights > 0) / (len(Z) * neighbors.shape[1]):.4f}")

    return True

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)

    print("通用流形解码器完整演示")
    print("="*60)

    print("\n第1部分：核心功能演示")
    print("-" * 30)
    # 运行主要演示
    demonstrate_universal_decoder()

    print("\n第2部分：详细网络可视化")
    print("-" * 30)
    # 运行详细网络可视化演示
    demonstrate_network_visualization()

    print("\n第3部分：图结构分析")
    print("-" * 30)
    # 运行图结构分析
    demonstrate_graph_analysis()

    print("\n第4部分：权重模式对比")
    print("-" * 30)
    # 运行权重模式对比
    demonstrate_weight_modes()

    print("\n所有演示完成！")
    print("="*60)
    print("生成的可视化文件:")
    print("  - universal_decoder_demo.png (核心功能)")
    print("  - detailed_network_visualization.png (详细网络图)")
    print("  - graph_structure_analysis.png (图结构分析)")
    print("="*60)